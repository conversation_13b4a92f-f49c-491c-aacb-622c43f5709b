#!/usr/bin/env python3
"""
Migration Script: From Beast to Pro Architecture

This script helps migrate from the old, conflicting architecture to the new
unified architecture. It performs the following tasks:

1. Updates imports to use unified systems
2. Migrates configuration from hardcoded to external files
3. Replaces dangerous patterns with safe alternatives
4. Validates the migration was successful

Run this script to automatically fix the critical architectural issues.
"""

import os
import re
import sys
import json
import shutil
import logging
from pathlib import Path
from typing import Dict, List, Tuple, Optional

logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

class ArchitecturalMigrator:
    """Migrates codebase from old architecture to unified architecture"""
    
    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root)
        self.src_dir = self.project_root / "src" / "knowledge_app"
        self.backup_dir = self.project_root / "migration_backup"
        
        # Migration statistics
        self.files_processed = 0
        self.imports_updated = 0
        self.patterns_fixed = 0
        self.errors = []
        
        logger.info(f"🔧 Architectural Migrator initialized: {self.project_root}")
    
    def run_migration(self) -> bool:
        """Run the complete migration process"""
        try:
            logger.info("🚀 Starting architectural migration...")
            
            # Step 1: Create backup
            self.create_backup()
            
            # Step 2: Migrate model manager imports
            self.migrate_model_manager_imports()
            
            # Step 3: Fix Pydantic usage
            self.fix_pydantic_usage()
            
            # Step 4: Migrate configuration
            self.migrate_configuration()
            
            # Step 5: Fix PyQt ownership issues
            self.fix_pyqt_ownership()
            
            # Step 6: Validate migration
            success = self.validate_migration()
            
            # Step 7: Generate report
            self.generate_migration_report()
            
            return success
            
        except Exception as e:
            logger.error(f"Migration failed: {e}")
            return False
    
    def create_backup(self):
        """Create backup of critical files before migration"""
        logger.info("📦 Creating backup...")
        
        if self.backup_dir.exists():
            shutil.rmtree(self.backup_dir)
        self.backup_dir.mkdir()
        
        # Files to backup
        backup_files = [
            "main.py",
            "main_optimized.py",
            "src/knowledge_app/core/model_manager.py",
            "src/knowledge_app/core/singleton_model_manager.py",
            "src/knowledge_app/core/async_model_manager.py",
            "src/knowledge_app/core/numpy_pydantic_fix.py",
            "complete_gguf_setup.py"
        ]
        
        for file_path in backup_files:
            source = self.project_root / file_path
            if source.exists():
                dest = self.backup_dir / file_path
                dest.parent.mkdir(parents=True, exist_ok=True)
                shutil.copy2(source, dest)
                logger.debug(f"Backed up: {file_path}")
        
        logger.info("✅ Backup created")
    
    def migrate_model_manager_imports(self):
        """Migrate from old model managers to unified inference manager"""
        logger.info("🔄 Migrating model manager imports...")
        
        # Import patterns to replace
        old_imports = [
            (r'from \.\.core\.model_manager import ModelManager', 
             'from ..core.unified_inference_manager import get_inference_manager'),
            (r'from \.\.core\.singleton_model_manager import.*', 
             'from ..core.unified_inference_manager import get_inference_manager'),
            (r'from \.\.core\.async_model_manager import.*', 
             'from ..core.unified_inference_manager import get_inference_manager'),
            (r'from knowledge_app\.core\.model_manager import.*', 
             'from knowledge_app.core.unified_inference_manager import get_inference_manager'),
        ]
        
        # Usage patterns to replace
        usage_patterns = [
            (r'ModelManager\(\)', 'get_inference_manager()'),
            (r'SingletonModelManager\(\)', 'get_inference_manager()'),
            (r'AsyncModelManager\(\)', 'get_inference_manager()'),
            (r'get_model_manager\(\)', 'get_inference_manager()'),
        ]
        
        # Process Python files
        for py_file in self.src_dir.rglob("*.py"):
            if self._update_file_patterns(py_file, old_imports + usage_patterns):
                self.files_processed += 1
                logger.debug(f"Updated: {py_file.relative_to(self.project_root)}")
        
        logger.info(f"✅ Model manager imports migrated ({self.files_processed} files)")
    
    def fix_pydantic_usage(self):
        """Replace monkey-patching with proper Pydantic usage"""
        logger.info("🔄 Fixing Pydantic usage...")
        
        patterns = [
            # Replace numpy_pydantic_fix imports
            (r'from \.numpy_pydantic_fix import.*', 
             'from .proper_pydantic_config import ProperBaseModel, suppress_pydantic_warnings'),
            (r'from \.\.core\.numpy_pydantic_fix import.*', 
             'from ..core.proper_pydantic_config import ProperBaseModel, suppress_pydantic_warnings'),
            
            # Replace BaseModel with ProperBaseModel
            (r'class (\w+)\(BaseModel\):', r'class \1(ProperBaseModel):'),
            
            # Replace apply_comprehensive_numpy_fix calls
            (r'apply_comprehensive_numpy_fix\(\)', 'suppress_pydantic_warnings()'),
        ]
        
        for py_file in self.src_dir.rglob("*.py"):
            if self._update_file_patterns(py_file, patterns):
                self.patterns_fixed += 1
        
        logger.info(f"✅ Pydantic usage fixed ({self.patterns_fixed} patterns)")
    
    def migrate_configuration(self):
        """Migrate from hardcoded configuration to external files"""
        logger.info("🔄 Migrating configuration...")
        
        # Create config directory
        config_dir = self.project_root / "config"
        config_dir.mkdir(exist_ok=True)
        
        # Create default configuration
        default_config = {
            "models": {
                "mimo_7b_q8": {
                    "name": "MiMo-7B-RL-Q8_0",
                    "type": "gguf",
                    "path": "models/gguf_models/MiMo-7B-RL-Q8_0.gguf",
                    "quantization": "Q8_0"
                }
            },
            "default_model": "mimo_7b_q8"
        }
        
        config_file = config_dir / "app_config.json"
        with open(config_file, 'w') as f:
            json.dump(default_config, f, indent=2)
        
        # Update files that use hardcoded paths
        self._fix_hardcoded_paths()
        
        logger.info("✅ Configuration migrated to external files")
    
    def fix_pyqt_ownership(self):
        """Fix PyQt widget ownership issues"""
        logger.info("🔄 Fixing PyQt ownership...")
        
        # Patterns to fix widget creation without parents
        patterns = [
            # Add parent parameter to common widget constructors
            (r'QPushButton\(([^,)]+)\)(?!\s*,\s*parent)', r'QPushButton(\1, parent=self)'),
            (r'QLabel\(([^,)]+)\)(?!\s*,\s*parent)', r'QLabel(\1, parent=self)'),
            (r'QWidget\(\)(?!\s*,\s*parent)', r'QWidget(parent=self)'),
        ]
        
        ui_files = list(self.src_dir.glob("ui/**/*.py"))
        for ui_file in ui_files:
            if self._update_file_patterns(ui_file, patterns):
                logger.debug(f"Fixed PyQt ownership: {ui_file.relative_to(self.project_root)}")
        
        logger.info("✅ PyQt ownership issues fixed")
    
    def validate_migration(self) -> bool:
        """Validate that the migration was successful"""
        logger.info("🧪 Validating migration...")
        
        success = True
        
        # Check that unified systems exist
        required_files = [
            "src/knowledge_app/core/unified_inference_manager.py",
            "src/knowledge_app/core/proper_pydantic_config.py",
            "src/knowledge_app/core/proper_config_manager.py",
            "src/knowledge_app/core/unified_application_launcher.py",
            "config/app_config.json"
        ]
        
        for file_path in required_files:
            if not (self.project_root / file_path).exists():
                logger.error(f"Missing required file: {file_path}")
                success = False
        
        # Check for remaining dangerous patterns
        dangerous_patterns = [
            (r'apply_comprehensive_numpy_fix', "Monkey-patching still present"),
            (r'complete_gguf_setup.*modify.*source', "Self-modifying code still present"),
            (r'qt_safe_access', "Qt safe access still being used"),
        ]
        
        for py_file in self.src_dir.rglob("*.py"):
            content = py_file.read_text(encoding='utf-8', errors='ignore')
            for pattern, description in dangerous_patterns:
                if re.search(pattern, content):
                    logger.warning(f"Dangerous pattern found in {py_file}: {description}")
        
        if success:
            logger.info("✅ Migration validation passed")
        else:
            logger.error("❌ Migration validation failed")
        
        return success
    
    def generate_migration_report(self):
        """Generate a detailed migration report"""
        logger.info("📊 Generating migration report...")
        
        report = f"""
# Architectural Migration Report

## Summary
- Files processed: {self.files_processed}
- Imports updated: {self.imports_updated}
- Patterns fixed: {self.patterns_fixed}
- Errors encountered: {len(self.errors)}

## Changes Made

### 1. Model Management
- Replaced multiple model managers with unified inference manager
- Updated all imports to use `get_inference_manager()`
- Eliminated "Four Horsemen" architecture conflict

### 2. Pydantic Configuration
- Replaced monkey-patching with proper Pydantic APIs
- Updated models to use `ProperBaseModel`
- Eliminated dangerous runtime modifications

### 3. Configuration Management
- Created external configuration files
- Replaced hardcoded paths with config references
- Eliminated self-modifying code patterns

### 4. PyQt Ownership
- Fixed widget creation to include parent parameters
- Reduced need for qt_safe_access utility
- Improved memory management

## Next Steps

1. Test the application thoroughly
2. Remove deprecated files once migration is validated
3. Update documentation to reflect new architecture
4. Train team on new patterns and practices

## Backup Location
Original files backed up to: {self.backup_dir}

## Errors
"""
        
        for error in self.errors:
            report += f"- {error}\n"
        
        report_file = self.project_root / "MIGRATION_REPORT.md"
        with open(report_file, 'w') as f:
            f.write(report)
        
        logger.info(f"📄 Migration report saved: {report_file}")
    
    def _update_file_patterns(self, file_path: Path, patterns: List[Tuple[str, str]]) -> bool:
        """Update file with pattern replacements"""
        try:
            content = file_path.read_text(encoding='utf-8')
            original_content = content
            
            for old_pattern, new_pattern in patterns:
                content = re.sub(old_pattern, new_pattern, content)
            
            if content != original_content:
                file_path.write_text(content, encoding='utf-8')
                return True
            
            return False
            
        except Exception as e:
            error_msg = f"Error updating {file_path}: {e}"
            self.errors.append(error_msg)
            logger.error(error_msg)
            return False
    
    def _fix_hardcoded_paths(self):
        """Fix hardcoded model paths in source files"""
        # Look for files with hardcoded model paths
        pattern = r'models/gguf_models/[^"\']*\.gguf'
        
        for py_file in self.src_dir.rglob("*.py"):
            try:
                content = py_file.read_text(encoding='utf-8')
                if re.search(pattern, content):
                    # Replace with config manager call
                    new_content = re.sub(
                        r'"models/gguf_models/([^"]*\.gguf)"',
                        r'get_config_manager().get_model_path("\1") or "models/gguf_models/\1"',
                        content
                    )
                    
                    if new_content != content:
                        py_file.write_text(new_content, encoding='utf-8')
                        logger.debug(f"Fixed hardcoded paths in: {py_file}")
                        
            except Exception as e:
                logger.warning(f"Could not process {py_file}: {e}")

def main():
    """Run the architectural migration"""
    print("🏗️ Knowledge App - Architectural Migration")
    print("=" * 50)
    print("This will migrate from the old architecture to the unified architecture.")
    print("A backup will be created before making any changes.")
    print("=" * 50)
    
    # Confirm migration
    response = input("Proceed with migration? (y/N): ").lower().strip()
    if response != 'y':
        print("Migration cancelled.")
        return
    
    # Run migration
    migrator = ArchitecturalMigrator()
    success = migrator.run_migration()
    
    if success:
        print("\n" + "=" * 50)
        print("🎉 MIGRATION COMPLETED SUCCESSFULLY!")
        print("=" * 50)
        print("✅ Unified inference manager implemented")
        print("✅ Proper Pydantic configuration applied")
        print("✅ External configuration system created")
        print("✅ PyQt ownership issues fixed")
        print("\n💡 Next steps:")
        print("1. Test the application: python main.py")
        print("2. Review the migration report")
        print("3. Remove deprecated files when ready")
        print("=" * 50)
    else:
        print("\n" + "=" * 50)
        print("❌ MIGRATION FAILED")
        print("=" * 50)
        print("Check the logs and migration report for details.")
        print("Original files are backed up in migration_backup/")
        print("=" * 50)

if __name__ == "__main__":
    main()
