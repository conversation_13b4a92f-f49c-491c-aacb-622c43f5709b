"""
Unified Inference Manager - Single Source of Truth for All Model Operations

This replaces the "Four Horsemen" (SingletonModelManager, AsyncModelManager, 
ModelManager, GGUFModelInference) with a single, clean, unified system.

Key Design Principles:
1. GGUF-Only: Uses only llama-cpp-python for local inference (most stable)
2. Single Path: No fallback mechanisms - one clear path to success
3. Process Isolation: Cloud inference runs in separate process
4. Memory Safe: Proper cleanup and resource management
5. Thread Safe: All operations are properly synchronized
"""

import logging
import threading
import time
import weakref
from typing import Optional, Dict, Any, Union, Callable
from enum import Enum
from dataclasses import dataclass
from pathlib import Path

logger = logging.getLogger(__name__)

class InferenceMode(Enum):
    """Available inference modes"""
    LOCAL_GGUF = "local_gguf"
    CLOUD_API = "cloud_api"
    OFFLINE = "offline"

class InferenceState(Enum):
    """Inference engine states"""
    UNLOADED = "unloaded"
    LOADING = "loading"
    READY = "ready"
    ERROR = "error"
    UNLOADING = "unloading"

@dataclass
class InferenceRequest:
    """Request for inference operation"""
    request_id: str
    operation: str  # 'load', 'unload', 'generate'
    params: Dict[str, Any]
    callback: Optional[Callable] = None
    timestamp: float = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = time.time()

class UnifiedInferenceManager:
    """
    Unified manager for all inference operations.
    
    This is the ONLY class that should be used for model operations.
    All other model managers are deprecated and will be removed.
    """
    
    _instance = None
    _lock = threading.Lock()
    _initialized = False
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if self._initialized:
            return
            
        with self._lock:
            if self._initialized:
                return
                
            self._initialized = True
            
            # State management
            self._state = InferenceState.UNLOADED
            self._mode = InferenceMode.LOCAL_GGUF  # Default to GGUF
            self._state_lock = threading.RLock()
            
            # Engine instances
            self._gguf_engine = None
            self._cloud_engine = None
            
            # Resource management
            self._active_resources = weakref.WeakSet()
            self._cleanup_handlers = []
            
            # Request handling
            self._request_queue = []
            self._processing_request = False
            
            logger.info("🏗️ UnifiedInferenceManager initialized")
    
    def set_mode(self, mode: InferenceMode) -> bool:
        """
        Set the inference mode.
        
        Args:
            mode: The inference mode to use
            
        Returns:
            bool: True if mode was set successfully
        """
        with self._state_lock:
            if self._state == InferenceState.LOADING:
                logger.warning("Cannot change mode while loading")
                return False
                
            old_mode = self._mode
            self._mode = mode
            
            # Unload current engine if switching modes
            if old_mode != mode and self._state == InferenceState.READY:
                self._unload_current_engine()
                
            logger.info(f"🔄 Inference mode changed: {old_mode.value} → {mode.value}")
            return True
    
    def get_state(self) -> InferenceState:
        """Get current inference state"""
        with self._state_lock:
            return self._state
    
    def get_mode(self) -> InferenceMode:
        """Get current inference mode"""
        return self._mode
    
    def is_ready(self) -> bool:
        """Check if inference engine is ready"""
        return self.get_state() == InferenceState.READY
    
    def load_model(self, model_path: Optional[str] = None, **kwargs) -> bool:
        """
        Load model for inference.
        
        Args:
            model_path: Path to model file (for GGUF mode)
            **kwargs: Additional parameters
            
        Returns:
            bool: True if model loaded successfully
        """
        with self._state_lock:
            if self._state == InferenceState.LOADING:
                logger.warning("Model is already loading")
                return False
                
            if self._state == InferenceState.READY:
                logger.info("Model is already loaded")
                return True
                
            self._state = InferenceState.LOADING
            
        try:
            if self._mode == InferenceMode.LOCAL_GGUF:
                return self._load_gguf_model(model_path, **kwargs)
            elif self._mode == InferenceMode.CLOUD_API:
                return self._load_cloud_model(**kwargs)
            else:
                logger.error(f"Unsupported inference mode: {self._mode}")
                return False
                
        except Exception as e:
            logger.error(f"Failed to load model: {e}")
            with self._state_lock:
                self._state = InferenceState.ERROR
            return False
    
    def _load_gguf_model(self, model_path: Optional[str], **kwargs) -> bool:
        """Load GGUF model using llama-cpp-python"""
        try:
            # Import GGUF engine
            from .gguf_model_inference import GGUFModelInference
            
            # Use provided path or default
            if model_path is None:
                model_path = "models/gguf_models/MiMo-7B-RL-Q8_0.gguf"
            
            # Create and configure GGUF engine
            self._gguf_engine = GGUFModelInference(model_path=model_path)
            
            # Load the model
            if self._gguf_engine.load_model():
                with self._state_lock:
                    self._state = InferenceState.READY
                logger.info(f"✅ GGUF model loaded: {model_path}")
                return True
            else:
                logger.error("Failed to load GGUF model")
                self._gguf_engine = None
                with self._state_lock:
                    self._state = InferenceState.ERROR
                return False
                
        except Exception as e:
            logger.error(f"GGUF model loading error: {e}")
            self._gguf_engine = None
            with self._state_lock:
                self._state = InferenceState.ERROR
            return False
    
    def _load_cloud_model(self, **kwargs) -> bool:
        """Load cloud-based model"""
        try:
            # Import cloud engine
            from .cloud_inference import CloudInference
            
            # Create cloud engine
            self._cloud_engine = CloudInference(**kwargs)
            
            # Test connection
            if self._cloud_engine.test_connection():
                with self._state_lock:
                    self._state = InferenceState.READY
                logger.info("✅ Cloud model connected")
                return True
            else:
                logger.error("Failed to connect to cloud model")
                self._cloud_engine = None
                with self._state_lock:
                    self._state = InferenceState.ERROR
                return False
                
        except Exception as e:
            logger.error(f"Cloud model connection error: {e}")
            self._cloud_engine = None
            with self._state_lock:
                self._state = InferenceState.ERROR
            return False
    
    def generate(self, prompt: str, **kwargs) -> Optional[str]:
        """
        Generate text using the loaded model.
        
        Args:
            prompt: Input prompt
            **kwargs: Generation parameters
            
        Returns:
            Generated text or None if failed
        """
        if not self.is_ready():
            logger.error("No model loaded for generation")
            return None
            
        try:
            if self._mode == InferenceMode.LOCAL_GGUF and self._gguf_engine:
                return self._gguf_engine.generate(prompt, **kwargs)
            elif self._mode == InferenceMode.CLOUD_API and self._cloud_engine:
                return self._cloud_engine.generate(prompt, **kwargs)
            else:
                logger.error("No active inference engine")
                return None
                
        except Exception as e:
            logger.error(f"Generation failed: {e}")
            return None
    
    def unload_model(self) -> bool:
        """
        Unload the current model and free resources.
        
        Returns:
            bool: True if unloaded successfully
        """
        with self._state_lock:
            if self._state == InferenceState.UNLOADED:
                logger.info("No model to unload")
                return True
                
            self._state = InferenceState.UNLOADING
            
        try:
            self._unload_current_engine()
            
            with self._state_lock:
                self._state = InferenceState.UNLOADED
                
            logger.info("✅ Model unloaded successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to unload model: {e}")
            with self._state_lock:
                self._state = InferenceState.ERROR
            return False
    
    def _unload_current_engine(self):
        """Unload the current inference engine"""
        if self._gguf_engine:
            try:
                self._gguf_engine.unload()
            except Exception as e:
                logger.warning(f"Error unloading GGUF engine: {e}")
            finally:
                self._gguf_engine = None
                
        if self._cloud_engine:
            try:
                self._cloud_engine.disconnect()
            except Exception as e:
                logger.warning(f"Error disconnecting cloud engine: {e}")
            finally:
                self._cloud_engine = None
    
    def shutdown(self):
        """Shutdown the inference manager"""
        logger.info("🛑 Shutting down UnifiedInferenceManager")
        
        # Unload model
        self.unload_model()
        
        # Run cleanup handlers
        for handler in self._cleanup_handlers:
            try:
                handler()
            except Exception as e:
                logger.warning(f"Cleanup handler error: {e}")
        
        logger.info("✅ UnifiedInferenceManager shutdown complete")

# Global instance getter
def get_inference_manager() -> UnifiedInferenceManager:
    """Get the unified inference manager instance"""
    return UnifiedInferenceManager()
