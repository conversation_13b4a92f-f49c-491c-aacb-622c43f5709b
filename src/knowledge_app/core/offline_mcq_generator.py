# src/knowledge_app/core/offline_mcq_generator.py

import logging
import os
import json
import re
from .gguf_model_inference import GGUFModelInference
from typing import Dict, Any

logger = logging.getLogger(__name__)

class OfflineMCQGenerator:
    """
    The definitive, stable offline generator.
    This class ONLY uses the GGUF engine via llama-cpp-python.
    It has no knowledge of the old, unstable transformers/bitsandbytes engine.
    This is the final solution to the crashing problem.
    """
    def __init__(self):
        self.gguf_engine = None
        self.is_initialized = False
        # IMPORTANT: Using the SUPERIOR MiMo-7B model that BEATS OpenAI o1-mini!
        # Try multiple possible model paths
        possible_paths = [
            "models/gguf_models/MiMo-7B-RL-Q8_0.gguf",
            "models/gguf_models/llama-2-7b-chat.Q8_0.gguf",
            "models/gguf_models/mimo-7b-rl-q8_0.gguf",
            "models/gguf_models/mimo-7b-rl-q6_k.gguf"
        ]

        self.gguf_model_path = None
        for path in possible_paths:
            if os.path.exists(path):
                self.gguf_model_path = path
                logger.info(f"🎯 Found GGUF model at: {path}")
                break

        if not self.gguf_model_path:
            self.gguf_model_path = possible_paths[0]  # Default to first option
            logger.warning(f"⚠️ No GGUF model found, will attempt: {self.gguf_model_path}")

        logger.info("🔧 OfflineMCQGenerator (GGUF-Only) with SUPERIOR MiMo-7B model created.")

    def initialize(self) -> bool:
        """
        Initializes the GGUF engine. Fails if the GGUF model cannot be loaded.
        NO MORE FALLBACKS. We commit to the stable engine.
        """
        if self.is_initialized:
            logger.info("✅ GGUF engine is already initialized.")
            return True

        logger.info("🚀 Attempting to initialize the stable GGUF engine...")
        if not os.path.exists(self.gguf_model_path):
            logger.error(f"❌ CRITICAL: GGUF model file not found at '{self.gguf_model_path}'")
            logger.error("   Please download the SUPERIOR MiMo-7B model and place it in the correct directory.")
            logger.error("   🚀 RECOMMENDED: jedisct1/MiMo-7B-RL-GGUF (BEATS OpenAI o1-mini!)")
            logger.error("   📁 File: mimo-7b-rl-q6_k.gguf (6.26 GB)")
            logger.error("   🔗 URL: https://huggingface.co/jedisct1/MiMo-7B-RL-GGUF")
            self.is_initialized = False
            return False

        self.gguf_engine = GGUFModelInference(self.gguf_model_path)
        if self.gguf_engine.load_model():
            self.is_initialized = True
            logger.info("🚀🚀🚀 MIMO-7B READY - SUPERIOR TO OPENAI o1-mini! 🚀🚀🚀")
            logger.info("🎯 95.8% on MATH-500 | 68.2% on AIME 2024 | 57.8% on LiveCodeBench v5")
            logger.info("🔥 This model BEATS OpenAI o1-mini in reasoning tasks!")
            return True
        else:
            logger.error("❌ CRITICAL: Failed to load the MiMo-7B model. The offline generator is not available.")
            self.is_initialized = False
            return False

    async def generate_quiz_async(self, context: str, topic: str, difficulty: str = "medium") -> Dict[str, Any]:
        """
        Generates a quiz question using ONLY the stable GGUF engine.
        """
        if not self.is_initialized:
            logger.error("❌ Cannot generate question: Offline GGUF engine is not initialized.")
            # Return a specific error message to the user
            return self._generate_error_fallback("The AI model is not configured. Please check the model file path in the settings.")

        try:
            prompt = self._create_inquisitor_prompt(context, topic, difficulty)
            response = self.gguf_engine.generate_text(prompt)
            return self._parse_mcq_response(response)
        except Exception as e:
            logger.error(f"❌ An error occurred during GGUF-based generation: {e}")
            return self._generate_error_fallback(f"An error occurred during question generation: {e}")

    def _create_inquisitor_prompt(self, context: str, topic: str, difficulty: str) -> str:
        # This is your high-quality prompt template. No changes needed here.
        difficulty_map = {
            "easy": "a high-school student",
            "medium": "an undergraduate university student",
            "hard": "a graduate student specializing in the field",
        }
        difficulty_level = difficulty_map.get(difficulty.lower(), "an undergraduate university student")

        prompt = f"""
### ROLE & GOAL ###
You are a meticulous university professor designing an exam. Your goal is to create ONE challenging multiple-choice question that tests a deep understanding of a key concept from the provided context about '{topic}'.

### CONTEXT ###
{context}

### TASK & CONSTRAINTS ###
1.  **Identify a Core Concept:** From the CONTEXT, identify a single, non-obvious concept.
2.  **Formulate the Question:** Create a clear, specific question about this core concept.
3.  **Create the Correct Answer:** Write a correct answer that is a thoughtful paraphrase of information in the context.
4.  **Create Plausible Distractors:** Generate three incorrect but highly plausible "distractor" options.
5.  **Write the Explanation:** Provide a concise explanation for why the correct answer is correct.

### NEGATIVE CONSTRAINTS (IMPORTANT) ###
- **DO NOT** create meta-level questions (e.g., "What is a key concept of...").
- **DO NOT** use the exact phrasing from the context in the options.

### STRICT OUTPUT FORMAT ###
You MUST respond with ONLY a single, raw JSON object.

{{
  "question": "Your question text here.",
  "options": {{ "A": "...", "B": "...", "C": "...", "D": "..." }},
  "correct": "B",
  "explanation": "Your detailed explanation here."
}}
"""
        return prompt

    def _parse_mcq_response(self, response: str) -> Dict[str, Any]:
        # This is your parser. No changes needed here.
        try:
            # Find the JSON object within the response string
            match = re.search(r'\{.*\}', response, re.DOTALL)
            if match:
                json_str = match.group(0)
                return json.loads(json_str)
            else:
                logger.error("No valid JSON object found in the model's response.")
                return self._generate_error_fallback("The AI model returned an invalid format.")
        except Exception as e:
            logger.error(f"Failed to parse model response: {e}")
            return self._generate_error_fallback("Failed to parse the AI model's response.")

    def _generate_error_fallback(self, error_text: str) -> Dict[str, Any]:
        return {
            "question": "Failed to Generate Question",
            "options": {
                "A": "Error",
                "B": "Please try again",
                "C": "Check model configuration",
                "D": "Contact support"
            },
            "correct": "A",
            "explanation": f"An error occurred: {error_text}"
        }
