"""
Proper Configuration Manager - No Self-Modifying Code

This replaces the dangerous self-modifying code pattern with a clean,
maintainable configuration system that uses external config files.

Key Principles:
1. Configuration in files, not hardcoded in source
2. No runtime modification of source code
3. Type-safe configuration with validation
4. Environment-specific configurations
5. Easy deployment and CI/CD compatibility
"""

import json
import logging
import os
from pathlib import Path
from typing import Dict, Any, Optional, Union, List
from dataclasses import dataclass, asdict
from enum import Enum

try:
    from .proper_pydantic_config import ProperBaseModel
except ImportError:
    # Fallback if ProperBaseModel is not available
    try:
        from pydantic import BaseModel as ProperBaseModel
    except ImportError:
        # Final fallback - create a simple base class
        class ProperBaseModel:
            def __init__(self, **kwargs):
                for key, value in kwargs.items():
                    setattr(self, key, value)

            def model_dump(self):
                return {k: v for k, v in self.__dict__.items() if not k.startswith('_')}

logger = logging.getLogger(__name__)

class ModelType(Enum):
    """Supported model types"""
    GGUF = "gguf"
    TRANSFORMERS = "transformers"
    CLOUD_API = "cloud_api"

class QuantizationType(Enum):
    """Supported quantization types"""
    Q4_K_M = "Q4_K_M"
    Q6_K = "Q6_K"
    Q8_0 = "Q8_0"
    FP16 = "FP16"
    FP32 = "FP32"

@dataclass
class ModelConfig:
    """Configuration for a specific model"""
    name: str
    type: ModelType
    path: str
    quantization: Optional[QuantizationType] = None
    max_tokens: int = 4096
    temperature: float = 0.7
    top_p: float = 0.9
    gpu_layers: int = -1  # -1 means auto-detect
    batch_size: int = 512
    context_length: int = 4096
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization"""
        result = asdict(self)
        # Convert enums to their values
        if self.type:
            result['type'] = self.type.value
        if self.quantization:
            result['quantization'] = self.quantization.value
        return result
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ModelConfig':
        """Create from dictionary"""
        # Convert enum values back to enums
        if 'type' in data:
            data['type'] = ModelType(data['type'])
        if 'quantization' in data and data['quantization']:
            data['quantization'] = QuantizationType(data['quantization'])
        return cls(**data)

@dataclass
class TrainingConfig:
    """Configuration for model training"""
    learning_rate: float = 2e-4
    batch_size: int = 4
    max_steps: int = 1000
    warmup_steps: int = 100
    save_steps: int = 500
    eval_steps: int = 250
    lora_rank: int = 16
    lora_alpha: int = 32
    lora_dropout: float = 0.1
    target_modules: List[str] = None
    
    def __post_init__(self):
        if self.target_modules is None:
            self.target_modules = ["q_proj", "v_proj", "k_proj", "o_proj"]

@dataclass
class UIConfig:
    """Configuration for UI settings"""
    theme: str = "dark"
    font_size: int = 12
    window_width: int = 1200
    window_height: int = 800
    enable_animations: bool = True
    auto_save: bool = True
    language: str = "en"

class ApplicationConfig(ProperBaseModel):
    """Main application configuration"""
    
    # Model configurations
    models: Dict[str, ModelConfig] = {}
    default_model: str = "mimo_7b_q8"
    
    # Training configuration
    training: TrainingConfig = TrainingConfig()
    
    # UI configuration
    ui: UIConfig = UIConfig()
    
    # Paths
    model_directory: str = "models"
    data_directory: str = "data"
    cache_directory: str = "cache"
    log_directory: str = "logs"
    
    # API settings
    api_timeout: int = 30
    max_retries: int = 3
    
    # Performance settings
    max_memory_usage: float = 0.8  # 80% of available memory
    enable_gpu: bool = True
    enable_mixed_precision: bool = True
    
    class Config:
        # Allow extra fields for forward compatibility
        extra = "allow"

class ProperConfigManager:
    """
    Proper configuration manager that uses external files.
    
    This completely replaces the self-modifying code pattern with
    a clean, maintainable approach.
    """
    
    def __init__(self, config_dir: str = "config"):
        self.config_dir = Path(config_dir)
        self.config_dir.mkdir(exist_ok=True)
        
        # Configuration files
        self.main_config_file = self.config_dir / "app_config.json"
        self.user_config_file = self.config_dir / "user_config.json"
        self.model_config_file = self.config_dir / "model_config.json"
        
        # Loaded configuration
        self._config: Optional[ApplicationConfig] = None
        self._user_overrides: Dict[str, Any] = {}
        
        logger.info(f"🔧 ConfigManager initialized: {self.config_dir}")
    
    def load_config(self) -> ApplicationConfig:
        """Load configuration from files"""
        try:
            # Start with default configuration
            config_data = self._get_default_config()
            
            # Load main configuration if it exists
            if self.main_config_file.exists():
                with open(self.main_config_file, 'r') as f:
                    main_config = json.load(f)
                    config_data.update(main_config)
            
            # Load user overrides if they exist
            if self.user_config_file.exists():
                with open(self.user_config_file, 'r') as f:
                    self._user_overrides = json.load(f)
                    config_data.update(self._user_overrides)
            
            # Create configuration object
            self._config = ApplicationConfig(**config_data)
            
            logger.info("✅ Configuration loaded successfully")
            return self._config
            
        except Exception as e:
            logger.error(f"Failed to load configuration: {e}")
            # Return default configuration on error
            return ApplicationConfig(**self._get_default_config())
    
    def save_config(self, config: Optional[ApplicationConfig] = None):
        """Save configuration to files"""
        try:
            if config is None:
                config = self._config
            
            if config is None:
                logger.warning("No configuration to save")
                return
            
            # Convert to dictionary
            config_dict = config.model_dump()
            
            # Save main configuration
            with open(self.main_config_file, 'w') as f:
                json.dump(config_dict, f, indent=2, default=str)
            
            logger.info("✅ Configuration saved successfully")
            
        except Exception as e:
            logger.error(f"Failed to save configuration: {e}")
    
    def get_config(self) -> ApplicationConfig:
        """Get current configuration"""
        if self._config is None:
            self._config = self.load_config()
        return self._config
    
    def update_model_path(self, model_name: str, new_path: str):
        """
        Update model path in configuration.
        
        This replaces the dangerous self-modifying code pattern.
        """
        try:
            config = self.get_config()
            
            if model_name in config.models:
                config.models[model_name].path = new_path
            else:
                # Create new model configuration
                config.models[model_name] = ModelConfig(
                    name=model_name,
                    type=ModelType.GGUF,
                    path=new_path
                )
            
            # Save the updated configuration
            self.save_config(config)
            
            logger.info(f"✅ Updated model path: {model_name} → {new_path}")
            
        except Exception as e:
            logger.error(f"Failed to update model path: {e}")
    
    def get_model_config(self, model_name: str) -> Optional[ModelConfig]:
        """Get configuration for a specific model"""
        config = self.get_config()
        return config.models.get(model_name)
    
    def get_model_path(self, model_name: str) -> Optional[str]:
        """Get path for a specific model"""
        model_config = self.get_model_config(model_name)
        return model_config.path if model_config else None
    
    def set_default_model(self, model_name: str):
        """Set the default model"""
        config = self.get_config()
        config.default_model = model_name
        self.save_config(config)
        logger.info(f"✅ Default model set to: {model_name}")
    
    def _get_default_config(self) -> Dict[str, Any]:
        """Get default configuration"""
        return {
            "models": {
                "mimo_7b_q8": {
                    "name": "MiMo-7B-RL-Q8_0",
                    "type": "gguf",
                    "path": "models/gguf_models/MiMo-7B-RL-Q8_0.gguf",
                    "quantization": "Q8_0",
                    "max_tokens": 4096,
                    "temperature": 0.7,
                    "top_p": 0.9,
                    "gpu_layers": -1,
                    "batch_size": 512,
                    "context_length": 4096
                },
                "mistral_7b": {
                    "name": "Mistral-7B-Instruct-v0.2",
                    "type": "transformers",
                    "path": "mistralai/Mistral-7B-Instruct-v0.2",
                    "quantization": None,
                    "max_tokens": 4096,
                    "temperature": 0.7,
                    "top_p": 0.9,
                    "gpu_layers": -1,
                    "batch_size": 4,
                    "context_length": 4096
                }
            },
            "default_model": "mimo_7b_q8",
            "training": {
                "learning_rate": 2e-4,
                "batch_size": 4,
                "max_steps": 1000,
                "warmup_steps": 100,
                "save_steps": 500,
                "eval_steps": 250,
                "lora_rank": 16,
                "lora_alpha": 32,
                "lora_dropout": 0.1,
                "target_modules": ["q_proj", "v_proj", "k_proj", "o_proj"]
            },
            "ui": {
                "theme": "dark",
                "font_size": 12,
                "window_width": 1200,
                "window_height": 800,
                "enable_animations": True,
                "auto_save": True,
                "language": "en"
            },
            "model_directory": "models",
            "data_directory": "data",
            "cache_directory": "cache",
            "log_directory": "logs",
            "api_timeout": 30,
            "max_retries": 3,
            "max_memory_usage": 0.8,
            "enable_gpu": True,
            "enable_mixed_precision": True
        }

# Global instance
_config_manager: Optional[ProperConfigManager] = None

def get_config_manager() -> ProperConfigManager:
    """Get the global configuration manager instance"""
    global _config_manager
    if _config_manager is None:
        _config_manager = ProperConfigManager()
    return _config_manager

def get_app_config() -> ApplicationConfig:
    """Get the current application configuration"""
    return get_config_manager().get_config()

def update_model_path(model_name: str, new_path: str):
    """Update model path in configuration (replaces self-modifying code)"""
    get_config_manager().update_model_path(model_name, new_path)
