"""
Proper Pydantic Configuration - No Monkey Patching Required

This module provides the CORRECT way to handle NumPy arrays and other custom types
in Pydantic without dangerous monkey-patching that breaks on library updates.

Key Principles:
1. Use Pydantic's official APIs only
2. No runtime modification of library internals
3. Proper type validation and serialization
4. Future-proof against Pydantic updates
"""

import logging
import warnings
from typing import Any, Type, Union
import numpy as np

# Handle different Pydantic versions gracefully
try:
    from pydantic import BaseModel, ConfigDict, Field
    from pydantic_core import core_schema
    from pydantic import GetCoreSchemaHandler
    PYDANTIC_V2 = True
except ImportError:
    try:
        from pydantic import BaseModel, Field
        from pydantic import validator as field_validator
        PYDANTIC_V2 = False
        # Create dummy classes for v1 compatibility
        class ConfigDict(dict):
            pass
        class core_schema:
            @staticmethod
            def any_schema():
                return {}
        class GetCoreSchemaHandler:
            pass
    except ImportError:
        # No Pydantic available, create minimal stubs
        class BaseModel:
            pass
        class Field:
            def __init__(self, *args, **kwargs):
                pass
        class ConfigDict(dict):
            pass
        class core_schema:
            @staticmethod
            def any_schema():
                return {}
        PYDANTIC_V2 = False

# Import field_validator for compatibility
if PYDANTIC_V2:
    try:
        from pydantic import field_validator
    except ImportError:
        def field_validator(*args, **kwargs):
            def decorator(func):
                return func
            return decorator
else:
    def field_validator(*args, **kwargs):
        def decorator(func):
            return func
        return decorator

logger = logging.getLogger(__name__)

class ProperBaseModel(BaseModel):
    """
    Base model with proper configuration for custom types.
    
    This is the CORRECT way to handle NumPy arrays and other custom types
    without monkey-patching Pydantic internals.
    """
    
    model_config = ConfigDict(
        # Allow arbitrary types (including NumPy arrays)
        arbitrary_types_allowed=True,
        # Validate assignment to catch type errors early
        validate_assignment=True,
        # Use enum values for better serialization
        use_enum_values=True,
        # Extra fields are forbidden by default for data integrity
        extra='forbid',
        # Validate default values
        validate_default=True,
        # Strict mode for better type safety
        str_strip_whitespace=True,
    )

class NumpyArrayField:
    """
    Proper NumPy array field implementation using Pydantic's official APIs.
    
    This replaces the dangerous monkey-patching approach with a clean,
    maintainable solution that works with any Pydantic version.
    """
    
    @classmethod
    def __get_pydantic_core_schema__(
        cls, 
        source_type: Any, 
        handler: GetCoreSchemaHandler
    ) -> core_schema.CoreSchema:
        """
        Generate Pydantic core schema for NumPy arrays.
        
        This is the official way to define custom type schemas in Pydantic v2.
        """
        
        def validate_numpy_array(value: Any) -> np.ndarray:
            """Validate and convert input to NumPy array"""
            if value is None:
                raise ValueError("NumPy array cannot be None")
            
            if isinstance(value, np.ndarray):
                return value
            
            # Try to convert to NumPy array
            try:
                return np.array(value)
            except Exception as e:
                raise ValueError(f"Cannot convert to NumPy array: {e}")
        
        def serialize_numpy_array(value: np.ndarray) -> list:
            """Serialize NumPy array to JSON-compatible format"""
            return value.tolist()
        
        return core_schema.no_info_after_validator_function(
            validate_numpy_array,
            core_schema.any_schema(),
            serialization=core_schema.to_jsonable_schema(
                core_schema.no_info_plain_validator_function(serialize_numpy_array)
            )
        )

# Type alias for NumPy arrays with proper Pydantic support
NumpyArray = Union[np.ndarray, list]

def create_numpy_field(
    default: Any = None,
    description: str = None,
    **kwargs
) -> Field:
    """
    Create a properly configured field for NumPy arrays.
    
    Args:
        default: Default value for the field
        description: Field description
        **kwargs: Additional field parameters
        
    Returns:
        Configured Pydantic field
    """
    return Field(
        default=default,
        description=description or "NumPy array field",
        **kwargs
    )

class ModelWithNumpyArrays(ProperBaseModel):
    """
    Example model showing proper NumPy array handling.
    
    This demonstrates the correct way to include NumPy arrays in Pydantic models
    without any monkey-patching or dangerous runtime modifications.
    """
    
    # NumPy array field with proper validation
    data: np.ndarray = create_numpy_field(
        default_factory=lambda: np.array([]),
        description="Data array"
    )
    
    # Optional NumPy array
    weights: Union[np.ndarray, None] = create_numpy_field(
        default=None,
        description="Optional weights array"
    )
    
    # Metadata fields
    name: str = Field(description="Model name")
    version: str = Field(default="1.0", description="Model version")
    
    @field_validator('data', mode='before')
    @classmethod
    def validate_data_array(cls, v):
        """Validate the data array"""
        if v is None:
            return np.array([])
        
        if isinstance(v, (list, tuple)):
            return np.array(v)
        
        if isinstance(v, np.ndarray):
            return v
        
        # Try to convert
        try:
            return np.array(v)
        except Exception:
            raise ValueError("Cannot convert to NumPy array")
    
    @field_validator('weights', mode='before')
    @classmethod
    def validate_weights_array(cls, v):
        """Validate the weights array"""
        if v is None:
            return None
        
        if isinstance(v, (list, tuple)):
            return np.array(v)
        
        if isinstance(v, np.ndarray):
            return v
        
        # Try to convert
        try:
            return np.array(v)
        except Exception:
            raise ValueError("Cannot convert weights to NumPy array")

def suppress_pydantic_warnings():
    """
    Suppress specific Pydantic warnings in a clean way.
    
    This is much safer than monkey-patching and only suppresses
    the specific warnings we know are harmless.
    """
    
    # Suppress NumPy array schema warnings
    warnings.filterwarnings(
        "ignore",
        message=".*Unable to generate pydantic-core schema for.*numpy.ndarray.*",
        category=UserWarning,
        module="pydantic.*"
    )
    
    # Suppress arbitrary types warnings for known safe cases
    warnings.filterwarnings(
        "ignore",
        message=".*Set `arbitrary_types_allowed=True` in the model_config.*",
        category=UserWarning,
        module="pydantic.*"
    )
    
    logger.debug("✅ Pydantic warnings suppressed (clean approach)")

def create_model_with_numpy_support(model_name: str, **fields) -> Type[BaseModel]:
    """
    Dynamically create a Pydantic model with NumPy support.
    
    Args:
        model_name: Name for the new model class
        **fields: Field definitions
        
    Returns:
        New model class with proper NumPy support
    """
    
    # Create model config with NumPy support
    config = ConfigDict(
        arbitrary_types_allowed=True,
        validate_assignment=True,
        use_enum_values=True,
        extra='forbid'
    )
    
    # Create the model class
    model_class = type(
        model_name,
        (ProperBaseModel,),
        {
            'model_config': config,
            **fields
        }
    )
    
    return model_class

# Example usage and testing
def test_proper_numpy_handling():
    """Test the proper NumPy handling implementation"""
    
    try:
        # Create a model instance
        model = ModelWithNumpyArrays(
            name="test_model",
            data=[1, 2, 3, 4, 5],
            weights=np.array([0.1, 0.2, 0.3, 0.4, 0.5])
        )
        
        # Verify the arrays are properly converted
        assert isinstance(model.data, np.ndarray)
        assert isinstance(model.weights, np.ndarray)
        
        # Test serialization
        serialized = model.model_dump()
        assert isinstance(serialized['data'], list)
        assert isinstance(serialized['weights'], list)
        
        logger.info("✅ Proper NumPy handling test passed")
        return True
        
    except Exception as e:
        logger.error(f"❌ Proper NumPy handling test failed: {e}")
        return False

# Apply clean warning suppression when module is imported
suppress_pydantic_warnings()

# Verify the implementation works
if __name__ == "__main__":
    test_proper_numpy_handling()
