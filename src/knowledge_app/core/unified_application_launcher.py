"""
Unified Application Launcher - Single Entry Point

This replaces the conflicting startup architectures (main.py, main_optimized.py, 
application_bootstrapper.py) with a single, clean entry point.

Key Principles:
1. Single Entry Point: One clear path to start the application
2. Lazy Loading: Heavy imports deferred until needed
3. Proper Error Handling: Graceful degradation and clear error messages
4. Resource Management: Proper cleanup and memory management
5. Performance Monitoring: Built-in startup performance tracking
"""

import sys
import os
import time
import logging
import warnings
from pathlib import Path
from typing import Optional, Dict, Any

# CRITICAL: Set environment variables BEFORE any heavy imports
os.environ['PYTHONUNBUFFERED'] = '1'
os.environ['TOKENIZERS_PARALLELISM'] = 'false'
os.environ['TRANSFORMERS_VERBOSITY'] = 'error'
os.environ['DATASETS_VERBOSITY'] = 'error'
os.environ['PYTORCH_CUDA_ALLOC_CONF'] = 'max_split_size_mb:128'

# Suppress warnings for clean startup
warnings.filterwarnings("ignore")

# Setup minimal logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class UnifiedApplicationLauncher:
    """
    Unified application launcher that replaces all conflicting entry points.
    
    This is the ONLY class that should be used to start the application.
    All other launchers (main.py, main_optimized.py, etc.) are deprecated.
    """
    
    def __init__(self):
        self.start_time = time.time()
        self.app = None
        self.main_window = None
        self.config_manager = None
        self.inference_manager = None
        
        # Performance tracking
        self.phase_times = {}
        self.current_phase = None
        
        logger.info("🚀 Knowledge App - Unified Launcher")
    
    def start_phase(self, phase_name: str, target_time: float = None):
        """Start a performance tracking phase"""
        if self.current_phase:
            self.end_phase(self.current_phase)
        
        self.current_phase = phase_name
        self.phase_times[phase_name] = {
            'start': time.time(),
            'target': target_time
        }
        
        logger.debug(f"📊 Phase started: {phase_name}")
    
    def end_phase(self, phase_name: str):
        """End a performance tracking phase"""
        if phase_name in self.phase_times:
            phase_data = self.phase_times[phase_name]
            duration = time.time() - phase_data['start']
            phase_data['duration'] = duration
            
            # Check if we met the target
            target = phase_data.get('target')
            status = "✅" if not target or duration <= target else "⚠️"
            
            logger.debug(f"📊 Phase completed: {phase_name} - {duration:.3f}s {status}")
        
        self.current_phase = None
    
    def launch(self) -> int:
        """
        Launch the application with unified startup process.
        
        Returns:
            int: Application exit code
        """
        try:
            # Phase 1: Environment Setup
            self.start_phase("environment_setup", 0.1)
            self._setup_environment()
            self.end_phase("environment_setup")
            
            # Phase 2: Configuration Loading
            self.start_phase("configuration", 0.2)
            self._load_configuration()
            self.end_phase("configuration")
            
            # Phase 3: PyQt Application Creation
            self.start_phase("qt_application", 0.5)
            self._create_qt_application()
            self.end_phase("qt_application")
            
            # Phase 4: Core Services (Lazy)
            self.start_phase("core_services", 0.3)
            self._initialize_core_services()
            self.end_phase("core_services")
            
            # Phase 5: Main Window Creation
            self.start_phase("main_window", 1.0)
            self._create_main_window()
            self.end_phase("main_window")
            
            # Phase 6: Application Launch
            self.start_phase("application_run", 0.1)
            result = self._run_application()
            self.end_phase("application_run")
            
            # Log performance summary
            self._log_performance_summary()
            
            return result
            
        except KeyboardInterrupt:
            logger.info("👋 Application interrupted by user")
            return 0
        except Exception as e:
            logger.error(f"❌ Application launch failed: {e}", exc_info=True)
            return 1
        finally:
            self._cleanup()
    
    def _setup_environment(self):
        """Setup the application environment"""
        # Add src directory to path
        src_dir = Path(__file__).parent.parent.parent
        if str(src_dir) not in sys.path:
            sys.path.insert(0, str(src_dir))
        
        # Verify Python version
        if sys.version_info < (3, 8):
            raise RuntimeError("Python 3.8 or higher is required")
        
        # Setup additional environment variables
        os.environ['QT_AUTO_SCREEN_SCALE_FACTOR'] = '1'
        os.environ['QT_ENABLE_HIGHDPI_SCALING'] = '1'
        
        logger.debug("✅ Environment setup complete")
    
    def _load_configuration(self):
        """Load application configuration"""
        try:
            from .proper_config_manager import get_config_manager
            
            self.config_manager = get_config_manager()
            self.config = self.config_manager.load_config()
            
            logger.info("✅ Configuration loaded")
            
        except Exception as e:
            logger.warning(f"Configuration loading failed, using defaults: {e}")
            # Continue with default configuration
    
    def _create_qt_application(self):
        """Create and configure the PyQt application"""
        try:
            from PyQt5.QtWidgets import QApplication
            from PyQt5.QtCore import Qt
            
            # Create QApplication
            self.app = QApplication(sys.argv)
            
            # Configure application
            self.app.setStyle('Fusion')
            self.app.setApplicationName("Knowledge App")
            self.app.setApplicationVersion("3.0.0")
            self.app.setOrganizationName("Knowledge Systems")
            
            # Enable high DPI support
            self.app.setAttribute(Qt.AA_EnableHighDpiScaling, True)
            self.app.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
            
            logger.info("✅ PyQt application created")
            
        except Exception as e:
            logger.error(f"Failed to create PyQt application: {e}")
            raise
    
    def _initialize_core_services(self):
        """Initialize core services with lazy loading"""
        try:
            # Initialize inference manager (lazy)
            from .unified_inference_manager import get_inference_manager
            self.inference_manager = get_inference_manager()
            
            # Set default mode based on configuration
            if self.config and hasattr(self.config, 'default_model'):
                model_config = self.config_manager.get_model_config(self.config.default_model)
                if model_config and model_config.type.value == 'gguf':
                    from .unified_inference_manager import InferenceMode
                    self.inference_manager.set_mode(InferenceMode.LOCAL_GGUF)
            
            logger.info("✅ Core services initialized")
            
        except Exception as e:
            logger.warning(f"Core services initialization failed: {e}")
            # Continue without core services - they'll be initialized on demand
    
    def _create_main_window(self):
        """Create the main application window"""
        try:
            # Use legacy main window for now to avoid ProperBaseModel issues
            logger.info("Using legacy main window to avoid import issues")
            from ..ui.main_window import MainWindow
            self.main_window = MainWindow(config=self.config.model_dump() if self.config else None)
            logger.info("✅ Legacy main window created")

            # TODO: Re-enable enterprise main window once ProperBaseModel issues are resolved
            # try:
            #     from ..ui.enterprise_main_window import EnterpriseMainWindow
            #     self.main_window = EnterpriseMainWindow(
            #         config=self.config,
            #         inference_manager=self.inference_manager
            #     )
            #     logger.info("✅ Enterprise main window created")
            #
            # except ImportError:
            #     # Fallback to legacy main window
            #     logger.warning("Enterprise main window not available, using legacy")
            #     from ..ui.main_window import MainWindow
            #     self.main_window = MainWindow(config=self.config.model_dump() if self.config else None)
            #     logger.info("✅ Legacy main window created")
            
            # Configure window
            if self.config and hasattr(self.config, 'ui'):
                ui_config = self.config.ui
                self.main_window.resize(ui_config.window_width, ui_config.window_height)
            
        except Exception as e:
            logger.error(f"Failed to create main window: {e}")
            raise
    
    def _run_application(self) -> int:
        """Run the application event loop"""
        try:
            if not self.main_window:
                raise RuntimeError("No main window to show")
            
            # Show the main window
            self.main_window.show()
            
            # Log successful startup
            total_time = time.time() - self.start_time
            logger.info(f"🎉 Knowledge App started successfully in {total_time:.2f}s!")
            
            # Run the event loop
            return self.app.exec_()
            
        except Exception as e:
            logger.error(f"Application runtime error: {e}")
            return 1
    
    def _cleanup(self):
        """Cleanup resources"""
        try:
            # Cleanup inference manager
            if self.inference_manager:
                self.inference_manager.shutdown()
            
            # Cleanup main window
            if self.main_window:
                try:
                    self.main_window.close()
                except:
                    pass
            
            # Cleanup Qt application
            if self.app:
                try:
                    self.app.quit()
                except:
                    pass
            
            logger.debug("✅ Cleanup complete")
            
        except Exception as e:
            logger.warning(f"Cleanup error: {e}")
    
    def _log_performance_summary(self):
        """Log performance summary"""
        total_time = time.time() - self.start_time
        
        logger.info("📊 Startup Performance Summary:")
        logger.info(f"   Total Time: {total_time:.3f}s")
        
        for phase_name, phase_data in self.phase_times.items():
            duration = phase_data.get('duration', 0)
            target = phase_data.get('target')
            
            if target:
                status = "✅" if duration <= target else "⚠️"
                logger.info(f"   {phase_name}: {duration:.3f}s (target: {target:.3f}s) {status}")
            else:
                logger.info(f"   {phase_name}: {duration:.3f}s")

def launch_application() -> int:
    """
    Launch the Knowledge App using the unified launcher.
    
    This is the ONLY function that should be called to start the application.
    
    Returns:
        int: Application exit code
    """
    launcher = UnifiedApplicationLauncher()
    return launcher.launch()

# For backward compatibility
def main() -> int:
    """Main entry point for backward compatibility"""
    return launch_application()

if __name__ == "__main__":
    sys.exit(launch_application())
