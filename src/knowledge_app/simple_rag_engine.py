"""
Simple RAG Engine - Haystack-Free Implementation

This is a lightweight, dependency-free RAG engine that provides reliable
context retrieval without the complexity and compatibility issues of Haystack.
"""

import logging
import json
import os
from typing import List, Dict, Any, Optional
from pathlib import Path

logger = logging.getLogger(__name__)

class SimpleDocument:
    """Simple document representation"""
    def __init__(self, content: str, meta: Optional[Dict] = None):
        self.content = content
        self.meta = meta or {}

class SimpleRAGEngine:
    """
    Simple, reliable RAG engine without external dependencies.
    Uses basic text matching for retrieval.
    """
    
    def __init__(self, db_path: str = "simple_rag_db.json"):
        self.db_path = db_path
        self.documents: List[SimpleDocument] = []
        self.is_initialized = False
        
        logger.info(f"🛡️ SimpleRAGEngine created (db_path={db_path})")
    
    def initialize(self) -> bool:
        """Initialize the RAG engine"""
        try:
            # Try to load existing database
            if os.path.exists(self.db_path):
                self._load_from_file()
            
            # If no documents, populate with curated content
            if not self.documents:
                self._populate_knowledge_base()
                self._save_to_file()
            
            self.is_initialized = True
            logger.info(f"✅ SimpleRAGEngine initialized with {len(self.documents)} documents")
            return True
            
        except Exception as e:
            logger.error(f"❌ SimpleRAGEngine initialization failed: {e}")
            return False
    
    def _populate_knowledge_base(self):
        """Populate with curated educational content"""
        educational_content = [
            {
                "content": """Magnetism is a fundamental force of nature that arises from the motion of electric charges. 
                Magnetic fields are created by moving electric charges and can exert forces on other moving charges. 
                The Earth itself acts as a giant magnet with magnetic north and south poles. Magnetic field lines 
                always form closed loops, flowing from north to south pole outside a magnet and from south to north 
                inside the magnet. Ferromagnetic materials like iron, nickel, and cobalt can be magnetized and 
                retain their magnetic properties. Electromagnets are created by passing electric current through 
                a coil of wire, often wrapped around an iron core to amplify the magnetic field.""",
                "meta": {"topic": "magnetism", "subject": "physics", "level": "high_school"}
            },
            {
                "content": """Photosynthesis is the process by which plants convert light energy into chemical energy. 
                This process occurs in chloroplasts and involves two main stages: light-dependent reactions and 
                light-independent reactions (Calvin cycle). During photosynthesis, carbon dioxide and water are 
                converted into glucose and oxygen using sunlight. The overall equation is: 6CO2 + 6H2O + light energy 
                → C6H12O6 + 6O2. Chlorophyll is the green pigment that captures light energy. Photosynthesis is 
                crucial for life on Earth as it produces oxygen and forms the base of most food chains.""",
                "meta": {"topic": "photosynthesis", "subject": "biology", "level": "high_school"}
            },
            {
                "content": """The water cycle describes the continuous movement of water on, above, and below Earth's surface. 
                Key processes include evaporation from oceans and lakes, transpiration from plants, condensation 
                forming clouds, precipitation as rain or snow, and collection in bodies of water. Solar energy 
                drives the water cycle by providing heat for evaporation. Water vapor rises, cools, and condenses 
                around particles in the atmosphere. Precipitation returns water to Earth's surface, where it flows 
                as surface runoff or infiltrates into groundwater systems.""",
                "meta": {"topic": "water_cycle", "subject": "earth_science", "level": "middle_school"}
            },
            {
                "content": """Chemical reactions involve the breaking and forming of chemical bonds between atoms. 
                Reactants are transformed into products with different properties. Chemical equations represent 
                reactions using symbols and formulas. The law of conservation of mass states that matter cannot 
                be created or destroyed in chemical reactions. Catalysts speed up reactions without being consumed. 
                Types of reactions include synthesis, decomposition, single replacement, double replacement, and 
                combustion. Activation energy is the minimum energy required to start a reaction.""",
                "meta": {"topic": "chemical_reactions", "subject": "chemistry", "level": "high_school"}
            },
            {
                "content": """Cellular respiration is the process by which cells break down glucose to produce ATP energy. 
                It occurs in three main stages: glycolysis (in cytoplasm), Krebs cycle (in mitochondria), and 
                electron transport chain (in mitochondria). The overall equation is: C6H12O6 + 6O2 → 6CO2 + 6H2O + ATP. 
                Aerobic respiration requires oxygen and produces about 36-38 ATP molecules per glucose. Anaerobic 
                respiration (fermentation) occurs without oxygen and produces less ATP. Mitochondria are called 
                the powerhouses of the cell because they produce most cellular ATP.""",
                "meta": {"topic": "cellular_respiration", "subject": "biology", "level": "high_school"}
            },
            {
                "content": """Newton's laws of motion describe the relationship between forces and motion. 
                First law (inertia): Objects at rest stay at rest and objects in motion stay in motion unless 
                acted upon by an external force. Second law: Force equals mass times acceleration (F = ma). 
                Third law: For every action, there is an equal and opposite reaction. These laws explain 
                everyday phenomena like why seatbelts are important, how rockets work, and why it's harder 
                to push a heavy object than a light one.""",
                "meta": {"topic": "newton_laws", "subject": "physics", "level": "high_school"}
            }
        ]
        
        for item in educational_content:
            doc = SimpleDocument(item["content"], item["meta"])
            self.documents.append(doc)
        
        logger.info(f"✅ Populated knowledge base with {len(educational_content)} documents")
    
    def _save_to_file(self):
        """Save documents to JSON file"""
        try:
            data = []
            for doc in self.documents:
                data.append({
                    "content": doc.content,
                    "meta": doc.meta
                })
            
            with open(self.db_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            logger.debug(f"✅ Saved {len(data)} documents to {self.db_path}")
            
        except Exception as e:
            logger.warning(f"⚠️ Failed to save documents: {e}")
    
    def _load_from_file(self):
        """Load documents from JSON file"""
        try:
            with open(self.db_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            self.documents = []
            for item in data:
                doc = SimpleDocument(item["content"], item.get("meta", {}))
                self.documents.append(doc)
            
            logger.info(f"✅ Loaded {len(self.documents)} documents from {self.db_path}")
            
        except Exception as e:
            logger.warning(f"⚠️ Failed to load documents: {e}")
    
    def retrieve_context(self, query: str, top_k: int = 3) -> List[str]:
        """Retrieve relevant context using simple text matching"""
        if not self.is_initialized:
            if not self.initialize():
                return []
        
        try:
            query_lower = query.lower()
            query_words = [word for word in query_lower.split() if len(word) > 2]
            
            if not query_words:
                return []
            
            # Score documents based on keyword matches
            scored_docs = []
            for doc in self.documents:
                content_lower = doc.content.lower()
                score = 0
                
                # Count exact word matches
                for word in query_words:
                    score += content_lower.count(word) * 2  # Weight word matches
                
                # Bonus for topic matches in metadata
                topic = doc.meta.get('topic', '')
                if topic and any(word in topic.lower() for word in query_words):
                    score += 20  # High bonus for topic match
                
                # Bonus for subject matches
                subject = doc.meta.get('subject', '')
                if subject and any(word in subject.lower() for word in query_words):
                    score += 10
                
                if score > 0:
                    scored_docs.append((doc, score))
            
            # Sort by score and take top_k
            scored_docs.sort(key=lambda x: x[1], reverse=True)
            top_docs = scored_docs[:top_k]
            
            # Extract content
            context_chunks = [doc.content.strip() for doc, score in top_docs]
            
            logger.info(f"✅ Retrieved {len(context_chunks)} context chunks for query: {query}")
            return context_chunks
            
        except Exception as e:
            logger.error(f"❌ Context retrieval failed: {e}")
            return []
    
    def get_document_count(self) -> int:
        """Get the number of documents"""
        if not self.is_initialized:
            if not self.initialize():
                return 0
        return len(self.documents)
    
    def add_document(self, content: str, meta: Optional[Dict] = None):
        """Add a new document"""
        doc = SimpleDocument(content, meta)
        self.documents.append(doc)
        self._save_to_file()
        logger.info(f"✅ Added new document (total: {len(self.documents)})")

# Compatibility wrapper for existing code
class BulletproofRAGEngine(SimpleRAGEngine):
    """Compatibility wrapper that uses SimpleRAGEngine"""
    
    def __init__(self, db_path="simple_rag_db.json", embedding_model=None):
        # Ignore embedding_model parameter for compatibility
        super().__init__(db_path)
        logger.info("🔄 Using SimpleRAGEngine for maximum compatibility")

# Legacy compatibility
class RAGEngine(BulletproofRAGEngine):
    """Legacy compatibility wrapper"""
    
    def ask(self, question: str, top_k_retriever: int = 5, top_k_reader: int = 1) -> List[Dict]:
        """Legacy compatibility method"""
        logger.warning("⚠️ Legacy ask() method called - using retrieve_context")
        
        context_chunks = self.retrieve_context(question, top_k=top_k_retriever)
        
        # Return in legacy format for compatibility
        results = []
        for i, chunk in enumerate(context_chunks):
            results.append({
                'answer': chunk,
                'score': 1.0 - (i * 0.1),
                'context': chunk,
                'meta': {'source': 'simple_rag'}
            })
        
        return results
