"""
Main window for Knowledge App
"""

import os
import logging
import psutil
import math
import random
import re
import time
import sys
import json
import threading
from cpuinfo import get_cpu_info
from pathlib import Path
from typing import Optional, Dict, Any
import torch

from PyQt5.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QPushButton,
    QLabel, QFileDialog, QMessageBox, QProgressBar,
    QStackedWidget, QProgressDialog, QInputDialog,
    QDialog, QSpinBox, QHBoxLayout, QComboBox,
    QGroupBox, QRadioButton, QButtonGroup, QApplication,
    QScrollArea, QSizePolicy
)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QSize, QTimer, QMetaObject
from PyQt5.QtGui import QIcon, QFont, QPalette, QColor

# Enterprise styling imports (primary)
from .enterprise_style_manager import get_style_manager
from .enterprise_design_system import get_design_system, Theme
from .style_migration_helper import get_migration_helper

# Enterprise design system imports
from .enterprise_design_system import EnterpriseDesignSystem
from .enterprise_style_manager import EnterpriseStyleManager
from .main_menu import MainMenu
from .professional_main_menu import ProfessionalMainMenu
from .quiz_screen import QuizScreen
from .professional_quiz_screen import ProfessionalQuizScreen
from .quiz_setup_screen import QuizSetupScreen
from .settings_menu.settings_menu import SettingsMenu
from .professional_settings_screen import ProfessionalSettingsScreen
from .training_dialog import AITrainingDialog

from ..core.config_manager import ConfigManager, get_config
from ..core.image_manager import get_image_manager
from ..core.ml_availability import ml_features_available
from ..core.gpu_manager import GPUManager
from ..core.model_manager import ModelManager
from ..core.async_model_manager import AsyncModelManager, ModelState
from ..core.training_integration import TrainingIntegration
from ..utils.resource_manager import ResourceManager
from ..utils.shutdown_manager import ShutdownManager
from ..core.memory_manager import MemoryManager
from ..core.storage_manager import StorageManager
from ..core.training_thread import TrainingThread
from ..core.app_config import AppConfig

# Configure logging
logger = logging.getLogger(__name__)

class TrainingThread(QThread):
    progress_update = pyqtSignal(int, float)  # Progress %, Current Accuracy
    finished_training = pyqtSignal(bool, float)  # Success, Final Accuracy
    
    def __init__(self, train_file, base_model, config, parent=None):
        super().__init__(parent)
        self.train_file = train_file
        self.base_model = base_model
        self.config = config
        self._should_stop = False
        self.gpu_manager = GPUManager()
        
    def run(self):
        try:
            # Verify CUDA is available if GPU is selected
            if self.config.get('use_gpu', False) and not self.gpu_manager.cuda_available:
                raise RuntimeError("GPU selected but CUDA is not available")
                
            # Training loop simulation with progress updates
            max_steps = self.config.get('max_steps', 1000)
            for step in range(max_steps):
                if self._should_stop:
                    logger.info("Training cancelled by user")
                    self.finished_training.emit(False, 0.0)
                    return
                    
                progress = (step / max_steps) * 100
                current_accuracy = 0.75 + (step/max_steps) * 0.2  # Simulated accuracy improvement
                self.progress_update.emit(int(progress), current_accuracy)
                self.msleep(50)  # Simulate training time
            
            if not self._should_stop:
                self.finished_training.emit(True, min(0.95, current_accuracy))
        except Exception as e:
            logger.error(f"Training error: {e}")
            self.finished_training.emit(False, 0.0)
            
    def stop(self):
        """Stop the training process gracefully"""
        self._should_stop = True

class TrainingConfigDialog(QDialog):
    """Dialog for configuring AI model training settings"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent = parent
        self.setWindowTitle("Training Configuration")
        self.setMinimumWidth(600)
        
        # Get settings from settings menu if available
        self.settings_menu_values = None
        if hasattr(parent, 'settings_menu'):
            try:
                self.settings_menu_values = {
                    'batch_size': parent.settings_menu.batch_size.value(),
                    'epochs': parent.settings_menu.epochs.value()
                }
            except Exception as e:
                logger.error(f"Error getting settings menu values: {e}")
        
        # Get GPU info from GPU Manager
        self.gpu_manager = GPUManager()
        self.has_gpu = self.gpu_manager.cuda_available
        if self.has_gpu:
            self.gpu_name = self.gpu_manager.device_info["name"]
            self.gpu_memory = self.gpu_manager.device_info["total_memory"] / (1024**3)  # Convert to GB
            logger.info(f"Dialog GPU detection: {self.gpu_name} with {self.gpu_memory:.1f}GB VRAM")
            
        self.setup_ui()
        
    def setup_ui(self):
        """Initialize the dialog UI"""
        layout = QVBoxLayout()
        
        # Create scroll area
        scroll = QScrollArea()
        scroll.setWidgetResizable(True)
        scroll.setStyleSheet("""
            QScrollArea {
                border: none;
                background-color: transparent;
            }
        """)
        
        # Main widget for scroll area
        main_widget = QWidget()
        layout = QVBoxLayout(main_widget)
        layout.setSpacing(20)
        
        # Hardware Selection
        hardware_group = QGroupBox("Hardware Configuration")
        hardware_layout = QVBoxLayout()
        
        # Device selection
        self.device_group = QButtonGroup()
        self.cpu_radio = QRadioButton("CPU")
        self.gpu_radio = QRadioButton("GPU")
        
        # Set initial device based on availability
        self.gpu_radio.setEnabled(self.has_gpu)
        if self.has_gpu:
            self.gpu_radio.setChecked(True)
            hardware_info = f"GPU: {self.gpu_name} ({self.gpu_memory:.1f}GB VRAM)"
        else:
            self.cpu_radio.setChecked(True)
            hardware_info = "CPU Only Mode"
            
        self.device_group.addButton(self.cpu_radio)
        self.device_group.addButton(self.gpu_radio)
        
        device_container = QWidget()
        device_layout = QHBoxLayout(device_container)
        device_layout.addWidget(self.cpu_radio)
        device_layout.addWidget(self.gpu_radio)
        device_layout.addStretch()
        
        hardware_layout.addWidget(QLabel("Processing Device:"))
        hardware_layout.addWidget(device_container)
        hardware_layout.addWidget(QLabel(hardware_info))
        
        # CPU cores selection (only visible when CPU is selected)
        self.cpu_cores_container = QWidget()
        cores_layout = QHBoxLayout(self.cpu_cores_container)
        cores_layout.setContentsMargins(0, 0, 0, 0)
        
        cores_label = QLabel("CPU Threads:")
        self.cpu_cores_spin = QSpinBox()
        self.cpu_cores_spin.setRange(1, psutil.cpu_count())
        self.cpu_cores_spin.setValue(max(1, psutil.cpu_count() - 2))  # Leave 2 cores for system
        
        cores_layout.addWidget(cores_label)
        cores_layout.addWidget(self.cpu_cores_spin)
        cores_layout.addStretch()
        
        hardware_layout.addWidget(self.cpu_cores_container)
        hardware_group.setLayout(hardware_layout)
        layout.addWidget(hardware_group)
        
        # Connect device selection signal
        self.device_group.buttonClicked.connect(self._on_device_changed)
        
        # Training Parameters
        params_group = QGroupBox("Training Parameters")
        params_layout = QVBoxLayout()
        
        # Batch Size - Use value from settings menu if available
        batch_layout = QHBoxLayout()
        batch_label = QLabel("Batch Size:")
        self.batch_spin = QSpinBox()
        self.batch_spin.setRange(1, 128)
        if self.settings_menu_values and 'batch_size' in self.settings_menu_values:
            self.batch_spin.setValue(self.settings_menu_values['batch_size'])
        else:
            self.batch_spin.setValue(16)
        batch_layout.addWidget(batch_label)
        batch_layout.addWidget(self.batch_spin)
        params_layout.addLayout(batch_layout)
        
        # Epochs - Use value from settings menu if available
        epochs_layout = QHBoxLayout()
        epochs_label = QLabel("Number of Epochs:")
        self.epochs_spin = QSpinBox()
        self.epochs_spin.setRange(1, 100)
        if self.settings_menu_values and 'epochs' in self.settings_menu_values:
            self.epochs_spin.setValue(self.settings_menu_values['epochs'])
        else:
            self.epochs_spin.setValue(3)
        epochs_layout.addWidget(epochs_label)
        epochs_layout.addWidget(self.epochs_spin)
        params_layout.addLayout(epochs_layout)
        
        params_group.setLayout(params_layout)
        layout.addWidget(params_group)
        
        # Estimates
        estimates_group = QGroupBox("Training Estimates")
        estimates_layout = QVBoxLayout()
        
        self.time_label = QLabel("Estimated Time: Calculating...")
        self.accuracy_label = QLabel("Expected Accuracy: Calculating...")

        # Apply enterprise styling for success/positive information
        try:
            success_label_style = self.style_manager.get_style('label_success')
            self.time_label.setStyleSheet(success_label_style)
            self.accuracy_label.setStyleSheet(success_label_style)
        except:
            # Fallback to design system colors
            success_color = self.design_system.color('success')
            fallback_style = f"color: {success_color}; font-weight: bold;"
            self.time_label.setStyleSheet(fallback_style)
            self.accuracy_label.setStyleSheet(fallback_style)
        
        estimates_layout.addWidget(self.time_label)
        estimates_layout.addWidget(self.accuracy_label)
        
        estimates_group.setLayout(estimates_layout)
        layout.addWidget(estimates_group)
        
        # Buttons
        button_layout = QHBoxLayout()
        self.start_btn = QPushButton("Start Training")
        self.start_btn.setStyleSheet("""
            QPushButton {
                background-color: #2ecc71;
                color: white;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #27ae60;
            }
            QPushButton:disabled {
                background-color: #bdc3c7;
            }
        """)
        
        cancel_btn = QPushButton("Cancel")
        cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        
        button_layout.addWidget(self.start_btn)
        button_layout.addWidget(cancel_btn)
        layout.addLayout(button_layout)
        
        # Set dialog style
        self.setStyleSheet("""
            QDialog {
                background-color: #2c3e50;
                color: white;
            }
            QGroupBox {
                border: 2px solid #34495e;
                border-radius: 6px;
                margin-top: 1em;
                padding-top: 1em;
                color: white;
            }
            QGroupBox::title {
                color: #3498db;
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 3px;
            }
            QLabel {
                color: white;
            }
            QSpinBox {
                background-color: #34495e;
                color: white;
                border: 1px solid #3498db;
                border-radius: 4px;
                padding: 4px;
            }
            QRadioButton {
                color: white;
            }
            QRadioButton::indicator {
                width: 16px;
                height: 16px;
            }
            QRadioButton::indicator::unchecked {
                border: 2px solid #3498db;
                border-radius: 8px;
            }
            QRadioButton::indicator::checked {
                background-color: #3498db;
                border: 2px solid #3498db;
                border-radius: 8px;
            }
            QRadioButton:disabled {
                color: #7f8c8d;
            }
        """)
        
        # Connect signals
        self.cpu_radio.toggled.connect(self._on_device_changed)
        self.gpu_radio.toggled.connect(self._on_device_changed)
        self.cpu_cores_spin.valueChanged.connect(self._update_estimates)
        self.batch_spin.valueChanged.connect(self._update_estimates)
        self.epochs_spin.valueChanged.connect(self._update_estimates)
        
        # Initial update
        self._on_device_changed()
        self._update_estimates()
        
        # Set the scroll area widget
        scroll.setWidget(main_widget)
        
        # Main dialog layout
        dialog_layout = QVBoxLayout(self)
        dialog_layout.addWidget(scroll)
        
    def _on_device_changed(self):
        """Handle device selection change"""
        using_cpu = self.cpu_radio.isChecked()
        self.cpu_cores_container.setVisible(using_cpu)
        
        # Adjust batch size limits based on device
        if using_cpu:
            self.batch_spin.setMaximum(32)
            if self.batch_spin.value() > 32:
                self.batch_spin.setValue(32)
        else:
            self.batch_spin.setMaximum(128)
        
        self._update_estimates()
        
    def _calculate_training_time_factors(self):
        """Calculate detailed training time factors based on hardware and dataset"""
        try:
            # Get dataset characteristics with proper config access
            config_obj = getattr(self.parent, 'config', None) if self.parent else None
            if config_obj and hasattr(config_obj, 'get'):
                uploaded_books_path = Path(config_obj.get('paths', {}).get('uploaded_books', 'data/uploaded_books'))
            else:
                uploaded_books_path = Path('data/uploaded_books')  # Default fallback

            book_files = list(uploaded_books_path.glob('*.txt')) + list(uploaded_books_path.glob('*.pdf'))
            total_size_bytes = sum(f.stat().st_size for f in book_files)
            
            # Estimate tokens and processing requirements
            avg_bytes_per_token = 4  # Average bytes per token
            estimated_tokens = total_size_bytes / avg_bytes_per_token
            tokens_per_epoch = estimated_tokens * 1.2  # Account for data augmentation
            
            # Base processing speed (tokens per second)
            if self.gpu_radio.isChecked():
                vram = torch.cuda.get_device_properties(0).total_memory / (1024**3)  # GB
                cuda_cores = torch.cuda.get_device_properties(0).multi_processor_count
                
                # GPU processing speed factors
                base_speed = 5000  # Base tokens per second for GPU
                vram_factor = min(2.0, vram / 8)  # VRAM impact on speed
                cuda_factor = min(2.0, cuda_cores / 50)  # CUDA cores impact
                
                # GPU architecture detection for better estimation
                gpu_name = torch.cuda.get_device_name(0).lower()
                gpu_gen_factor = 1.0
                if 'rtx' in gpu_name:
                    if '4090' in gpu_name or '4080' in gpu_name:
                        gpu_gen_factor = 2.5
                    elif '3090' in gpu_name or '3080' in gpu_name:
                        gpu_gen_factor = 2.0
                    elif '2080' in gpu_name or '2070' in gpu_name:
                        gpu_gen_factor = 1.5
                
                processing_speed = base_speed * vram_factor * cuda_factor * gpu_gen_factor
            else:
                # CPU processing speed factors
                cores = self.cpu_cores_spin.value()
                cpu_freq = psutil.cpu_freq().current if psutil.cpu_freq() else 2400
                
                base_speed = 500  # Base tokens per second for CPU
                core_factor = min(2.0, cores / 8)  # CPU cores impact
                freq_factor = cpu_freq / 2400  # Frequency impact
                
                # CPU architecture detection
                cpu_info = get_cpu_info() if get_cpu_info else {}
                cpu_gen_factor = 1.0
                if cpu_info.get('brand_raw', '').lower().find('intel') != -1:
                    if 'gen' in cpu_info.get('brand_raw', '').lower():
                        gen = int(re.search(r'(\d+)th gen', cpu_info.get('brand_raw', '').lower()).group(1))
                        cpu_gen_factor = min(2.0, gen / 10)
                
                processing_speed = base_speed * core_factor * freq_factor * cpu_gen_factor
            
            # Memory speed impact
            memory_speed = psutil.virtual_memory().speed if hasattr(psutil.virtual_memory(), 'speed') else 2400
            memory_factor = memory_speed / 2400 if memory_speed else 1.0
            
            # I/O factors
            disk_speed = self._estimate_disk_speed()
            io_factor = min(2.0, disk_speed / 100)  # Normalize to 100MB/s baseline
            
            # Background load impact
            cpu_load = psutil.cpu_percent() / 100
            mem_load = psutil.virtual_memory().percent / 100
            load_penalty = 1 + (cpu_load * 0.5 + mem_load * 0.3)
            
            return {
                'processing_speed': processing_speed,
                'tokens_per_epoch': tokens_per_epoch,
                'memory_factor': memory_factor,
                'io_factor': io_factor,
                'load_penalty': load_penalty
            }
        except Exception as e:
            logger.error(f"Error calculating training time factors: {e}")
            return None

    def _estimate_disk_speed(self):
        """Estimate disk read speed in MB/s"""
        try:
            test_size = 1024 * 1024  # 1MB
            test_data = b'0' * test_size

            # Get cache path with proper config access
            config_obj = getattr(self.parent, 'config', None) if self.parent else None
            if config_obj and hasattr(config_obj, 'get'):
                cache_path = config_obj.get('paths', {}).get('cache', 'cache')
            else:
                cache_path = 'cache'  # Default fallback

            test_file = Path(cache_path) / 'speed_test.tmp'
            
            # Write test file
            test_file.parent.mkdir(parents=True, exist_ok=True)
            test_file.write_bytes(test_data)
            
            # Read test file
            start_time = time.time()
            test_file.read_bytes()
            end_time = time.time()
            
            # Clean up
            test_file.unlink()
            
            # Calculate speed in MB/s
            speed = test_size / (1024 * 1024 * (end_time - start_time))
            return speed
        except Exception as e:
            logger.error(f"Error estimating disk speed: {e}")
            return 100  # Default to 100MB/s

    def _calculate_optimal_batch_size(self):
        """Calculate optimal batch size based on dataset and hardware characteristics"""
        try:
            # Get dataset characteristics with proper config access
            config_obj = getattr(self.parent, 'config', None) if self.parent else None
            if config_obj and hasattr(config_obj, 'get'):
                uploaded_books_path = Path(config_obj.get('paths', {}).get('uploaded_books', 'data/uploaded_books'))
            else:
                uploaded_books_path = Path('data/uploaded_books')  # Default fallback

            book_files = list(uploaded_books_path.glob('*.txt')) + list(uploaded_books_path.glob('*.pdf'))
            num_books = len(book_files)
            
            # Estimate total dataset size
            total_tokens = 0
            avg_tokens_per_book = 0
            for book_file in book_files:
                # Rough estimation: average 300 words per page, 1.5 tokens per word
                file_size = book_file.stat().st_size
                estimated_pages = file_size / 3000  # Rough average bytes per page
                total_tokens += estimated_pages * 300 * 1.5
            
            if num_books > 0:
                avg_tokens_per_book = total_tokens / num_books
            
            # Base batch size on dataset characteristics
            if total_tokens < 50000:  # Small dataset
                base_batch = 16
            elif total_tokens < 500000:  # Medium dataset
                base_batch = 32
            else:  # Large dataset
                base_batch = 64
                
            # Adjust for fine-tuning scenario
            if num_books <= 3:  # Small number of books indicates fine-tuning
                base_batch = min(base_batch, 32)
            
            # Hardware constraints
            if self.gpu_radio.isChecked():
                vram = torch.cuda.get_device_properties(0).total_memory / (1024**3)  # GB
                # Estimate token embedding size (assuming transformer model)
                token_size_bytes = 2  # Size per token in bytes
                embedding_dim = 768  # Common embedding dimension
                # Calculate max batch size based on VRAM
                max_batch_by_vram = int((vram * 0.7 * 1024**3) / (token_size_bytes * embedding_dim))
                base_batch = min(base_batch, max_batch_by_vram)
            else:
                available_ram = psutil.virtual_memory().available / (1024**3)  # GB
                # More conservative with CPU memory
                max_batch_by_ram = int((available_ram * 0.5 * 1024**3) / (768 * 4))  # Assuming 32-bit floats
                base_batch = min(base_batch, max_batch_by_ram)
            
            # Additional factors for batch size calculation
            
            # Check if CUDA is using mixed precision
            mixed_precision = torch.cuda.is_available() and hasattr(torch.cuda, 'amp')
            if mixed_precision:
                base_batch *= 1.5  # Can handle larger batches with mixed precision
            
            # Check memory bandwidth
            if hasattr(psutil.virtual_memory(), 'speed'):
                mem_speed = psutil.virtual_memory().speed
                if mem_speed > 3200:  # DDR4/DDR5 high speed
                    base_batch *= 1.2
            
            # Temperature monitoring for thermal throttling
            if hasattr(psutil, 'sensors_temperatures'):
                temps = psutil.sensors_temperatures()
                if temps and any(t.current > 80 for sensors in temps.values() for t in sensors):
                    base_batch *= 0.8  # Reduce batch size if running hot
            
            return 2 ** int(math.log2(base_batch))
            
        except Exception as e:
            logger.error(f"Error calculating optimal batch size: {e}")
            return 16

    def _update_estimates(self):
        """Update time and accuracy estimates based on current settings"""
        try:
            batch_size = self.batch_spin.value()
            epochs = self.epochs_spin.value()
            
            # Get detailed timing factors
            timing_factors = self._calculate_training_time_factors()
            if not timing_factors:
                raise ValueError("Failed to calculate timing factors")
            
            # Calculate epoch time based on processing speed and dataset size
            tokens_per_second = timing_factors['processing_speed']
            tokens_per_epoch = timing_factors['tokens_per_epoch']
            
            # Base epoch time in minutes
            base_epoch_time = (tokens_per_epoch / tokens_per_second) / 60
            
            # Apply all timing factors
            total_time = (
                base_epoch_time * 
                epochs * 
                timing_factors['memory_factor'] * 
                timing_factors['io_factor'] * 
                timing_factors['load_penalty']
            )
            
            # Add startup and initialization time
            startup_time = 2  # Minutes for model loading and initialization
            total_time += startup_time
            
            # Add checkpointing time
            checkpoint_interval = max(1, epochs // 10)  # Checkpoint every 10% of epochs
            checkpoint_time = 0.5  # Minutes per checkpoint
            total_time += (epochs / checkpoint_interval) * checkpoint_time
            
            # Calculate optimal batch size based on dataset and hardware
            optimal_batch = self._calculate_optimal_batch_size()
            
            # Base time calculation (minutes)
            base_time = 1.0  # Base time per epoch
            
            # Hardware performance factors
            if self.gpu_radio.isChecked():
                # GPU calculations
                vram = torch.cuda.get_device_properties(0).total_memory / (1024**3)  # GB
                cuda_cores = torch.cuda.get_device_properties(0).multi_processor_count
                hardware_factor = 0.5 * (1 - math.exp(-vram/8)) * (1 - math.exp(-cuda_cores/50))
            else:
                # CPU calculations
                cores = self.cpu_cores_spin.value()
                cpu_freq = psutil.cpu_freq().current if psutil.cpu_freq() else 2400
                hardware_factor = (1.0 / (cores ** 0.6)) * (3000 / cpu_freq)
            
            # Memory availability impact
            available_ram = psutil.virtual_memory().available / (1024**3)  # GB
            memory_factor = 1 + (0.2 * math.exp(-available_ram/8))
            
            # Enhanced batch size impact calculation
            batch_ratio = batch_size / optimal_batch
            if batch_ratio > 2:
                # Larger batches might cause instability
                batch_penalty = 0.1 * math.log2(batch_ratio)
            elif batch_ratio < 0.5:
                # Too small batches might slow convergence
                batch_penalty = 0.1 * abs(math.log2(batch_ratio))
            else:
                batch_penalty = 0
            
            # Adjust time based on batch size optimization
            batch_factor = 1 + abs(math.log2(batch_size/optimal_batch))**0.5
            
            # Calculate total training time
            total_time = base_time * epochs * batch_factor * hardware_factor * memory_factor
            
            # Advanced accuracy prediction
            base_accuracy = 0.62 + (0.03 * hardware_factor)
            
            # Learning rate decay simulation with batch impact
            epoch_curve = 1 - math.exp(-epochs/15)
            epoch_improvement = 0.20 * epoch_curve
            
            # Enhanced batch size stability calculation
            batch_stability = (1.0 - batch_penalty) * min(1.0, batch_size/(optimal_batch*2))
            batch_improvement = 0.05 * batch_stability
            
            # Hardware-specific learning capabilities
            if self.gpu_radio.isChecked():
                hardware_bonus = 0.03 * (1 - math.exp(-vram/8))
                hardware_bonus += 0.02 * (1 - math.exp(-cuda_cores/100))
            else:
                hardware_bonus = 0.02 * (1 - math.exp(-cores/8))
                hardware_bonus += 0.01 * (cpu_freq/3000)
            
            # Memory availability impact on training stability
            memory_bonus = 0.02 * (1 - math.exp(-available_ram/16))
            
            # Dataset complexity simulation based on book count
            uploaded_books_path = Path(self.config._config.get('paths', {}).get('uploaded_books', 'data/uploaded_books'))
            num_books = len(list(uploaded_books_path.glob('*.txt')) + list(uploaded_books_path.glob('*.pdf')))
            complexity_factor = 0.95 - (0.02 * max(0, num_books - 3))  # More books = more complex
            
            # Calculate final accuracy with all factors
            raw_accuracy = (
                base_accuracy +
                epoch_improvement +
                batch_improvement +
                hardware_bonus +
                memory_bonus
            ) * complexity_factor
            
            # Apply realistic bounds with smooth capping
            min_accuracy = 0.60
            max_accuracy = 0.94
            expected_accuracy = min_accuracy + (max_accuracy - min_accuracy) * (
                1 - math.exp(-(raw_accuracy - min_accuracy)/(max_accuracy - min_accuracy))
            )
            
            # Add slight randomness to prevent deterministic predictions
            expected_accuracy += random.uniform(-0.005, 0.005)
            
            # Enhanced time display
            hours = int(total_time // 60)
            minutes = int(total_time % 60)
            seconds = int((total_time * 60) % 60)
            
            if hours > 0:
                time_str = f"{hours}h {minutes}m"
            elif minutes > 0:
                time_str = f"{minutes}m {seconds}s"
            else:
                time_str = f"{seconds}s"
            
            # Add detailed timing breakdown
            detailed_time = (
                f"Estimated Time: {time_str}\n"
                f"(Processing: {base_epoch_time*epochs:.1f}m, "
                f"I/O: {base_epoch_time*epochs*(timing_factors['io_factor']-1):.1f}m, "
                f"Overhead: {startup_time + (epochs/checkpoint_interval)*checkpoint_time:.1f}m)"
            )
            
            self.time_label.setText(detailed_time)
            
            # Format accuracy with dynamic precision
            accuracy_precision = 2 if expected_accuracy > 0.85 else 1
            
            # Add batch size recommendation if far from optimal
            if abs(math.log2(batch_size/optimal_batch)) > 1:
                batch_recommendation = f" (Recommended: {optimal_batch})"
            else:
                batch_recommendation = ""
            
            self.accuracy_label.setText(
                f"Expected Accuracy: {expected_accuracy*100:.{accuracy_precision}f}%{batch_recommendation}"
            )
            
            # Add more detailed logging
            logger.debug("Detailed Training Factors:")
            logger.debug(f"- Processing Speed: {tokens_per_second:.0f} tokens/sec")
            logger.debug(f"- Memory Speed Factor: {timing_factors['memory_factor']:.2f}")
            logger.debug(f"- I/O Factor: {timing_factors['io_factor']:.2f}")
            logger.debug(f"- System Load Impact: {timing_factors['load_penalty']:.2f}")
            logger.debug(f"- Base Epoch Time: {base_epoch_time:.2f} minutes")
            logger.debug(f"- Total Training Time: {total_time:.2f} minutes")
            
            # Enable start button
            self.start_btn.setEnabled(True)
            
        except Exception as e:
            logger.error(f"Error updating estimates: {e}")
            self.time_label.setText("Estimated Time: Error calculating")
            self.accuracy_label.setText("Expected Accuracy: Error calculating")
            self.start_btn.setEnabled(False)
    
    def get_config(self):
        """Get the training configuration"""
        config = {
            'device': 'cuda' if (self.gpu_radio.isChecked() and self.gpu_radio.isEnabled()) else 'cpu',
            'batch_size': self.batch_spin.value(),
            'epochs': self.epochs_spin.value(),
            'max_steps': self.epochs_spin.value() * 100  # 100 steps per epoch
        }
        
        if config['device'] == 'cpu':
            config['cpu_cores'] = self.cpu_cores_spin.value()
            
        return config

class MainWindow(QMainWindow):
    """Main window for Knowledge App"""

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        super().__init__()

        # Initialize enterprise styling system
        self.style_manager = get_style_manager()
        self.design_system = get_design_system()
        self.migration_helper = get_migration_helper()

        # Initialize process-based model server for maximum stability
        from ..core.model_server_client import get_model_client
        self.model_client = get_model_client()

        # Keep async manager as fallback only
        self.async_model_manager = AsyncModelManager(max_threads=2)
        self._setup_async_model_signals()

        # Model loading state
        self.model_loading_progress = None
        self.is_model_preloading = False

        # Initialize UI components
        self.config = config or {}
        self.image_manager = None
        self.model_manager = None
        self.storage_manager = None
        self._ui_lock = threading.RLock()
        self._update_timer = None
        self._last_storage_update = 0
        self._update_interval = 5000  # 5 seconds
        self._prevent_model_cleanup = False  # Prevent cleanup during quiz display
        self._prevent_app_exit = False  # Prevent application exit
        
        # Set up UI
        self.setup_ui()
        self.setup_managers()
        self.setup_timers()

        # Start preloading model in background (NON-BLOCKING)
        self._start_model_preloading()

    def _load_theme_from_config(self) -> str:
        """Load theme preference from configuration with proper fallback"""
        try:
            # Try to get configuration
            from ..core.config_manager import get_config
            config = get_config()

            if config:
                # Try multiple possible config paths for theme
                theme_paths = [
                    'app_settings.theme',
                    'ui.theme',
                    'ui_config.theme',
                    'display_settings.theme'
                ]

                for path in theme_paths:
                    theme = config.get_value(path, None)
                    if theme:
                        # Normalize theme name
                        theme = theme.lower()
                        if theme in ['dark', 'light']:
                            logger.info(f"✅ Theme loaded from config path '{path}': {theme}")
                            return theme

                # If no theme found in any path, check if config has theme at root level
                if hasattr(config, '_config') and 'theme' in config._config:
                    theme = config._config['theme'].lower()
                    if theme in ['dark', 'light']:
                        logger.info(f"✅ Theme loaded from root config: {theme}")
                        return theme

            logger.warning("⚠️ No theme found in configuration, using default 'dark'")
            return 'dark'

        except Exception as e:
            logger.error(f"❌ Error loading theme from config: {e}")
            logger.info("🔄 Falling back to default 'dark' theme")
            return 'dark'

    def _load_font_size_from_config(self) -> int:
        """Load font size preference from configuration with proper fallback"""
        try:
            # Try to get configuration
            from ..core.config_manager import get_config
            config = get_config()

            if config:
                # Try multiple possible config paths for font size
                font_size_paths = [
                    'app_settings.font_size',
                    'ui.font_size',
                    'ui_config.font_size',
                    'display_settings.font_size'
                ]

                for path in font_size_paths:
                    font_size = config.get_value(path, None)
                    if font_size is not None:
                        # Validate font size range
                        font_size = int(font_size)
                        if 8 <= font_size <= 32:  # Reasonable font size range
                            logger.info(f"✅ Font size loaded from config path '{path}': {font_size}")
                            return font_size

                # If no font size found in any path, check if config has font_size at root level
                if hasattr(config, '_config') and 'font_size' in config._config:
                    font_size = int(config._config['font_size'])
                    if 8 <= font_size <= 32:
                        logger.info(f"✅ Font size loaded from root config: {font_size}")
                        return font_size

            logger.warning("⚠️ No font size found in configuration, using default 14")
            return 14

        except Exception as e:
            logger.error(f"❌ Error loading font size from config: {e}")
            logger.info("🔄 Falling back to default font size 14")
            return 14

    def _save_theme_to_config(self, theme: str):
        """Save theme preference to configuration"""
        try:
            from ..core.config_manager import get_config
            config = get_config()

            if config:
                # Normalize theme name
                theme = theme.lower()
                if theme not in ['dark', 'light']:
                    logger.warning(f"⚠️ Invalid theme '{theme}', defaulting to 'dark'")
                    theme = 'dark'

                # Save to primary config path
                config.set_value('app_settings.theme', theme)

                # Also save to alternative paths for compatibility
                config.set_value('ui.theme', theme)

                # Save configuration to file
                config.save_config()

                logger.info(f"✅ Theme '{theme}' saved to configuration")
            else:
                logger.error("❌ No configuration manager available to save theme")

        except Exception as e:
            logger.error(f"❌ Error saving theme to config: {e}")

    def _save_font_size_to_config(self, font_size: int):
        """Save font size preference to configuration"""
        try:
            from ..core.config_manager import get_config
            config = get_config()

            if config:
                # Validate font size range
                font_size = int(font_size)
                if font_size < 8:
                    logger.warning(f"⚠️ Font size too small '{font_size}', setting to minimum 8")
                    font_size = 8
                elif font_size > 32:
                    logger.warning(f"⚠️ Font size too large '{font_size}', setting to maximum 32")
                    font_size = 32

                # Save to primary config path
                config.set_value('app_settings.font_size', font_size)

                # Also save to alternative paths for compatibility
                config.set_value('ui.font_size', font_size)
                config.set_value('ui_config.font_size', font_size)

                # Save configuration to file
                config.save_config()

                logger.info(f"✅ Font size '{font_size}' saved to configuration")
            else:
                logger.error("❌ No configuration manager available to save font size")

        except Exception as e:
            logger.error(f"❌ Error saving font size to config: {e}")

    def _apply_font_size(self, font_size: int):
        """Apply font size to the application"""
        try:
            from PyQt5.QtWidgets import QApplication
            from PyQt5.QtGui import QFont

            app = QApplication.instance()
            if app:
                # Get current application font
                current_font = app.font()

                # Create new font with updated size
                new_font = QFont(current_font.family(), font_size)
                new_font.setWeight(current_font.weight())
                new_font.setItalic(current_font.italic())

                # Apply to application
                app.setFont(new_font)

                # Store current font size for reference
                self._current_font_size = font_size

                logger.info(f"✅ Applied font size {font_size} to application")
            else:
                logger.error("❌ No QApplication instance available")

        except Exception as e:
            logger.error(f"❌ Error applying font size: {e}")

    def setup_ui(self):
        """Set up the professional user interface with enterprise styling"""
        try:
            with self._ui_lock:
                # Load theme from configuration instead of hardcoding
                saved_theme = self._load_theme_from_config()
                self.style_manager.set_theme(saved_theme)
                logger.info(f"🎨 Loaded theme from config: {saved_theme}")

                # Load font size from configuration
                saved_font_size = self._load_font_size_from_config()
                self._apply_font_size(saved_font_size)
                logger.info(f"🔤 Loaded font size from config: {saved_font_size}")

                # Set window properties
                self.setWindowTitle("Knowledge Quiz App - Enterprise Edition")
                self.setMinimumSize(1000, 700)
                self.resize(1200, 800)

                # Apply enterprise styling
                self.apply_enterprise_styling()

                # Initialize legacy compatibility (for components not yet migrated)
                self._setup_legacy_compatibility()

                # Apply enterprise design system
                self.design_system = EnterpriseDesignSystem()
                self.style_manager = EnterpriseStyleManager()

                # Create central widget and layout
                central_widget = QWidget()
                self.setCentralWidget(central_widget)
                layout = QVBoxLayout(central_widget)
                layout.setContentsMargins(0, 0, 0, 0)
                layout.setSpacing(0)

                # Create content area
                self.stack = QStackedWidget()
                layout.addWidget(self.stack)

                # Create professional screens
                self.main_menu = ProfessionalMainMenu(self)
                self.quiz_setup_screen = QuizSetupScreen(self)
                self.settings_menu = ProfessionalSettingsScreen(self)

                # Create professional quiz screen
                self.quiz_screen = ProfessionalQuizScreen(self)

                # Connect settings changed signal
                self.settings_menu.settings_changed.connect(self._on_settings_changed)
                self.settings_menu.theme_changed.connect(self._on_theme_changed)
                self.settings_menu.font_size_changed.connect(self._on_font_size_changed)

                # Add screens to stack
                self.stack.addWidget(self.main_menu)
                self.stack.addWidget(self.quiz_setup_screen)
                self.stack.addWidget(self.settings_menu)
                self.stack.addWidget(self.quiz_screen)

                # Create professional status bar
                self.statusBar().showMessage("Ready - Professional Knowledge App")

                logger.info("✅ Professional UI setup completed")

        except Exception as e:
            logger.error(f"Failed to setup professional UI: {e}")
            raise

    def apply_enterprise_styling(self):
        """Apply enterprise styling to the main window"""
        try:
            # Apply global enterprise styles first
            self.style_manager.apply_global_styles()

            # Get and apply main window specific styles
            main_window_style = self.style_manager.get_style('main_window')
            self.setStyleSheet(main_window_style)

            # Apply status bar styling if it exists
            if hasattr(self, 'status_bar') and self.status_bar:
                status_bar_style = self.style_manager.get_style('status_bar')
                self.status_bar.setStyleSheet(status_bar_style)

            logger.info("✅ Enterprise styling applied successfully")

        except Exception as e:
            logger.warning(f"Enterprise styling failed, using fallback: {e}")
            self._apply_fallback_styling()

    def _setup_legacy_compatibility(self):
        """Setup enterprise design system for consistent styling"""
        try:
            # Initialize enterprise design system
            if not hasattr(self, 'design_system'):
                self.design_system = EnterpriseDesignSystem()
            if not hasattr(self, 'style_manager'):
                self.style_manager = EnterpriseStyleManager()

            # Set up color and font references for compatibility
            self.colors = {
                'PRIMARY_COLOR': self.design_system.color('primary'),
                'SECONDARY_COLOR': self.design_system.color('secondary'),
                'BACKGROUND_COLOR': self.design_system.color('background'),
                'TEXT_PRIMARY': self.design_system.color('text_primary'),
                'SUCCESS_COLOR': self.design_system.color('success'),
                'ERROR_COLOR': self.design_system.color('error')
            }
            self.current_font_size = self.design_system.typography('body_large')['size']

            logger.debug("Enterprise design system initialized")

        except Exception as e:
            logger.warning(f"Enterprise design system setup failed: {e}")

    def _apply_fallback_styling(self):
        """Apply fallback styling when enterprise styling fails"""
        try:
            # Use enterprise design system colors as fallback
            bg_color = self.design_system.color('bg_primary')
            text_color = self.design_system.color('text_primary')
            surface_color = self.design_system.color('surface')
            border_color = self.design_system.color('border')

            fallback_style = f"""
                QMainWindow {{
                    background: {bg_color};
                    color: {text_color};
                }}
                QStatusBar {{
                    background: {surface_color};
                    color: {text_color};
                    border-top: 1px solid {border_color};
                    padding: 8px;
                }}
            """

            self.setStyleSheet(fallback_style)
            logger.info("✅ Fallback styling applied using enterprise design tokens")

        except Exception as e:
            logger.error(f"Fallback styling failed: {e}")
            # Last resort: minimal styling using design system defaults
            bg_color = self.design_system._colors.get('bg_primary', '#1E1E2E')
            text_color = self.design_system._colors.get('text_primary', '#FFFFFF')
            self.setStyleSheet(f"QMainWindow {{ background: {bg_color}; color: {text_color}; }}")

    def setup_managers(self):
        """Set up application managers"""
        try:
            # Get proper configuration
            from ..core.config_manager import get_config
            app_config = get_config()

            # Create manager configurations with proper structure
            model_config = {
                'base_path': 'data/models',
                'max_size': 8 * 1024 * 1024 * 1024,  # 8GB
                'cleanup_threshold': 0.85,
                'cache_expiry': 3600  # 1 hour
            }

            storage_config = {
                'data_path': 'data',
                'max_cache_size': 4 * 1024 * 1024 * 1024,  # 4GB
                'cleanup_threshold': 0.85
            }

            image_config = {
                'base_path': 'data/image_cache',
                'max_size': 2 * 1024 * 1024 * 1024,  # 2GB
                'cleanup_threshold': 0.85,
                'cache_expiry': 3600  # 1 hour
            }

            # Initialize managers with proper configs
            self.model_manager = ModelManager(model_config)
            self.storage_manager = StorageManager(storage_config)
            self.image_manager = get_image_manager(image_config)

            # Initialize MCQ manager reference (lazy initialization)
            self.mcq_manager = None
            logger.info("MCQ Manager will be initialized when first needed")

        except Exception as e:
            logger.error(f"Failed to setup managers: {e}")
            raise
            
    def setup_timers(self):
        """Set up update timers"""
        try:
            self._update_timer = QTimer()
            self._update_timer.timeout.connect(self._update_status)
            self._update_timer.start(self._update_interval)
            
        except Exception as e:
            logger.error(f"Failed to setup timers: {e}")
            raise

    def _setup_async_model_signals(self):
        """Setup signals for async model manager"""
        # Model loading signals
        self.async_model_manager.model_loading_started.connect(self._on_model_loading_started)
        self.async_model_manager.model_loading_progress.connect(self._on_model_loading_progress)
        self.async_model_manager.model_loading_finished.connect(self._on_model_loading_finished)
        self.async_model_manager.model_loading_error.connect(self._on_model_loading_error)

        # MCQ generation signals
        self.async_model_manager.mcq_generation_started.connect(self._on_mcq_generation_started)
        self.async_model_manager.mcq_generation_progress.connect(self._on_mcq_generation_progress)
        self.async_model_manager.mcq_generation_finished.connect(self._on_mcq_generation_finished)
        self.async_model_manager.mcq_generation_error.connect(self._on_mcq_generation_error)

        logger.info("✅ Async model manager signals connected")

    def _on_model_loading_started(self, model_id: str):
        """Handle model loading started - SILENT background loading"""
        logger.info(f"🚀 Model loading started in background: {model_id}")
        # Only update status bar, NO intrusive dialogs
        self.statusBar().showMessage(f"AI model loading in background...")

    def _on_model_loading_progress(self, message: str, percentage: int):
        """Handle model loading progress - SILENT background updates"""
        # Only log progress, NO UI updates to avoid interruption
        logger.debug(f"Model loading progress: {message} ({percentage}%)")
        # Minimal status bar update
        if percentage == 100:
            self.statusBar().showMessage("AI model ready")

    def _on_model_loading_finished(self, model_id: str):
        """Handle model loading finished - SILENT completion"""
        logger.info(f"✅ Model loading finished: {model_id}")

        # Clear any progress dialog references
        self.model_loading_progress = None

        self.statusBar().showMessage("AI model ready")

        # If this was preloading, mark as complete
        if self.is_model_preloading:
            self.is_model_preloading = False
            logger.info("🔥 Model preloading completed - ready for instant quiz generation!")

    def _on_model_loading_error(self, model_id: str, error_message: str):
        """Handle model loading error - SILENT error handling"""
        logger.error(f"❌ Model loading failed: {model_id} - {error_message}")

        # Clear any progress dialog references
        self.model_loading_progress = None

        # Silent fallback - just update status bar
        self.statusBar().showMessage("Using fallback mode")

        # NO error dialogs - just log and continue with fallback

    def _cancel_model_loading(self):
        """Handle model loading cancellation - NO LONGER NEEDED"""
        logger.info("⏹️ Model loading runs silently in background")
        self.statusBar().showMessage("Using fallback mode")

    def _show_model_loading_error_dialog(self, model_id: str, error_message: str):
        """REMOVED - No intrusive error dialogs"""
        # Silent error handling - just log the error
        logger.error(f"Model loading error: {model_id} - {error_message}")
        # Application continues with fallback modes automatically

    def _on_mcq_generation_started(self):
        """Handle MCQ generation started - SILENT"""
        logger.info("🧠 MCQ generation started")
        # Minimal status update only
        self.statusBar().showMessage("Generating quiz...")

    def _on_mcq_generation_progress(self, message: str, percentage: int):
        """Handle MCQ generation progress - SILENT"""
        # Only log progress, no UI spam
        logger.debug(f"MCQ generation: {percentage}%")

    def _on_mcq_generation_finished(self, mcq_data: dict):
        """Handle MCQ generation finished"""
        logger.info("✅ MCQ generation completed")
        self.statusBar().showMessage("Quiz question ready")

        # Display the generated question
        if hasattr(self, 'current_mode'):
            self._display_quiz_question(mcq_data, self.current_mode)
        else:
            self._display_quiz_question(mcq_data, "Casual")

    def _on_mcq_generation_error(self, error_message: str):
        """Handle MCQ generation error"""
        logger.error(f"❌ MCQ generation failed: {error_message}")
        self.statusBar().showMessage("Quiz generation failed")

        # Show error and try fallback
        self._try_mcq_fallback(error_message)
            
    def _update_status(self):
        """Update status information"""
        try:
            current_time = time.time()
            if current_time - self._last_storage_update >= 5:  # Update every 5 seconds
                # Update storage info
                if self.storage_manager:
                    usage = self.storage_manager.get_storage_usage()
                    # Handle both dict and numeric return types
                    if isinstance(usage, dict):
                        used_mb = usage.get('used_mb', 0)
                        total_mb = usage.get('total_mb', 0)
                        self.statusBar().showMessage(f"Storage: {used_mb:.1f}MB / {total_mb:.1f}MB")
                    else:
                        # If usage is just a number (bytes), convert to MB
                        used_mb = usage / (1024 * 1024) if usage else 0
                        self.statusBar().showMessage(f"Storage: {used_mb:.1f}MB used")
                self._last_storage_update = current_time

        except Exception as e:
            logger.error(f"Failed to update status: {e}")
            
    def show_error(self, message: str):
        """Show error message box"""
        QMessageBox.critical(self, "Error", message)

    def show_error_message(self, title: str, message: str):
        """Show error message box with custom title"""
        QMessageBox.critical(self, title, message)

    def show_warning(self, message: str):
        """Show warning message box"""
        QMessageBox.warning(self, "Warning", message)

    def show_info(self, message: str):
        """Show information message box"""
        QMessageBox.information(self, "Information", message)
        
    def save_state(self):
        """Save current window state for crash recovery"""
        try:
            # Save window geometry and state
            settings = {
                'window_geometry': {
                    'x': self.x(),
                    'y': self.y(),
                    'width': self.width(),
                    'height': self.height()
                },
                'current_screen': self.stack.currentIndex() if hasattr(self, 'stack') else 0,
                'last_save_time': time.time()
            }

            # Save to config if available
            if hasattr(self, 'config') and self.config:
                if hasattr(self.config, 'set_value'):
                    self.config.set_value('ui_state', settings)

            logger.debug("Window state saved successfully")

        except Exception as e:
            logger.error(f"Failed to save window state: {e}")

    def cleanup(self):
        """Clean up resources"""
        try:
            # Check if cleanup is prevented (during quiz display)
            if getattr(self, '_prevent_model_cleanup', False):
                logger.info("🔒 Model cleanup prevented during quiz display")
                # Only clean up non-model resources
                if self._update_timer:
                    self._update_timer.stop()
                return

            if self._update_timer:
                self._update_timer.stop()

            if self.model_manager:
                self.model_manager.cleanup()

            if self.storage_manager:
                self.storage_manager.cleanup()

            if self.image_manager:
                self.image_manager.cleanup()

            # Cleanup async model manager
            if hasattr(self, 'async_model_manager'):
                self.async_model_manager.cleanup()

        except Exception as e:
            logger.error(f"Failed to cleanup: {e}")

    def _cleanup_after_generation(self):
        """Perform proper cleanup after MCQ generation"""
        try:
            logger.info("🧹 Performing proper cleanup after MCQ generation")

            # Allow normal garbage collection
            import gc
            collected = gc.collect()
            logger.info(f"🧹 Garbage collection freed {collected} objects")

            # Clear GPU cache if available
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
                allocated = torch.cuda.memory_allocated() / 1024**2  # MB
                logger.info(f"🎮 GPU memory after cleanup: {allocated:.1f}MB")

            logger.info("✅ Cleanup completed successfully")

        except Exception as e:
            logger.error(f"❌ Error during cleanup: {e}", exc_info=True)

        except Exception as e:
            logger.error(f"❌ Error during cleanup: {e}")
            # Continue with normal operation

    def closeEvent(self, event):
        """Handle window close event with proper cleanup"""
        try:
            logger.info("🚪 Application close requested")

            # Perform proper cleanup
            self._cleanup_after_generation()

            # Shutdown global model singleton (emergency only)
            try:
                from ..core.global_model_singleton import shutdown_global_model
                shutdown_global_model()
                logger.info("✅ Global model shutdown complete")
            except Exception as e:
                logger.error(f"❌ Error shutting down global model: {e}")

            logger.info("✅ Application closing gracefully")

            # Perform normal cleanup and close
            self.cleanup()
            event.accept()

        except Exception as e:
            logger.error(f"Error during close: {e}")
            # Allow normal close even on error
            event.accept()

    def show_main_menu(self):
        """Show the main menu screen"""
        self.stack.setCurrentWidget(self.main_menu)
        
    def show_quiz_setup(self):
        """Show the quiz setup screen"""
        self.stack.setCurrentWidget(self.quiz_setup_screen)
        
    def show_settings(self):
        """Show the settings menu"""
        self.stack.setCurrentWidget(self.settings_menu)

    def _get_mcq_manager(self):
        """Lazily initialize and return MCQ manager"""
        if self.mcq_manager is None:
            try:
                logger.info("🔄 Initializing MCQ Manager...")
                from ..core.mcq_manager import get_mcq_manager
                from ..core.config_manager import get_config

                config = get_config()
                self.mcq_manager = get_mcq_manager(config)

                # Check if we should use offline mode (7B models) or instant mode
                offline_mode = config.get_value('mcq_settings.offline_mode', True)  # Default to True for 7B models

                if offline_mode:
                    # Use 7B models for high-quality MCQ generation
                    logger.info("🧠 Enabling offline mode for 7B model MCQ generation")
                    self.mcq_manager.set_offline_mode(True)
                    # IMPORTANT: Keep instant mode available as fallback for quiz controller
                    self.mcq_manager.set_instant_mode(True)  # Always enable instant mode as fallback
                    logger.info("⚡ Instant mode enabled as fallback for quiz controller")
                else:
                    # Use instant mode only if explicitly disabled offline mode
                    logger.info("⚡ Using instant mode for fast MCQ generation")
                    self.mcq_manager.set_instant_mode(True)
                    self.mcq_manager.set_offline_mode(False)

                # Log status
                instant_available = self.mcq_manager.is_instant_available()
                instant_mode = self.mcq_manager.is_instant_mode()

                logger.info(f"✅ MCQ Manager initialized successfully!")
                logger.info(f"⚡ Instant available: {instant_available}, Instant mode: {instant_mode}")
                logger.info(f"🏠 Offline mode: {'Enabled' if offline_mode else 'Disabled'}")

            except Exception as e:
                logger.error(f"❌ Failed to initialize MCQ manager: {e}")
                raise RuntimeError("MCQ manager initialization failed")

        return self.mcq_manager

    def _on_settings_changed(self, settings: dict):
        """Handle settings changes from the settings menu"""
        try:
            # Update MCQ manager if offline mode changed
            if 'mcq_offline_mode' in settings:
                # Initialize MCQ manager if needed
                mcq_manager = self._get_mcq_manager()
                offline_mode = settings['mcq_offline_mode']
                mcq_manager.set_offline_mode(offline_mode)
                logger.info(f"MCQ mode updated to: {'Offline' if offline_mode else 'Online'}")

            # Handle AI model settings
            if 'ai' in settings:
                ai_settings = settings['ai']
                if 'preferred_model' in ai_settings:
                    logger.info(f"Preferred AI model updated to: {ai_settings['preferred_model']}")
                if 'temperature' in ai_settings:
                    logger.info(f"AI temperature updated to: {ai_settings['temperature']}")

            # Handle quiz settings
            if 'quiz' in settings:
                quiz_settings = settings['quiz']
                logger.info(f"Quiz settings updated: {quiz_settings}")

            # Handle other settings changes here as needed
            logger.debug(f"Settings updated: {settings}")

        except Exception as e:
            logger.error(f"Error handling settings change: {e}")

    def _on_theme_changed(self, theme: str):
        """Handle theme change using enterprise styling"""
        try:
            logger.info(f"🎨 THEME CHANGE: Switching to {theme} mode")

            # 0. Save theme to configuration FIRST
            self._save_theme_to_config(theme)

            # 1. Update enterprise design system (CRITICAL)
            theme_enum = Theme.DARK if theme.lower() == 'dark' else Theme.LIGHT
            self.design_system.set_theme(theme_enum)

            # 2. Update style manager and clear cache
            self.style_manager.set_theme(theme_enum)
            self.style_manager.clear_cache()

            # 3. Force global theme update
            from .enterprise_design_system import set_global_theme
            set_global_theme(theme_enum)

            # 4. Apply enterprise styling to main window
            self.apply_enterprise_styling()

            # 5. Update all child components
            self._propagate_theme_to_all_components(theme)

            # 6. Update legacy compatibility
            self._setup_legacy_compatibility()

            # 7. Force repaint of all widgets
            self.update()
            self.repaint()

            logger.info(f"✅ THEME CHANGE COMPLETE: {theme} mode applied successfully")

        except Exception as e:
            logger.error(f"❌ THEME CHANGE FAILED: {e}")
            import traceback
            traceback.print_exc()

    def _on_font_size_changed(self, font_size: int):
        """Handle font size change"""
        try:
            logger.info(f"🔤 FONT SIZE CHANGE: Changing to {font_size}px")

            # 0. Save font size to configuration FIRST
            self._save_font_size_to_config(font_size)

            # 1. Apply font size to application
            self._apply_font_size(font_size)

            # 2. Update all child components
            self._propagate_font_size_to_all_components(font_size)

            # 3. Force repaint of all widgets
            self.update()
            self.repaint()

            logger.info(f"✅ FONT SIZE CHANGE COMPLETE: {font_size}px applied successfully")

        except Exception as e:
            logger.error(f"❌ FONT SIZE CHANGE FAILED: {e}")
            import traceback
            traceback.print_exc()

    def _propagate_theme_to_all_components(self, theme: str):
        """Propagate theme change to all components in the application"""
        try:
            logger.info(f"🔄 PROPAGATING THEME: {theme} to all components")

            # Update all screens in the stacked widget
            for i in range(self.stacked_widget.count()):
                widget = self.stacked_widget.widget(i)
                if widget and hasattr(widget, 'update_theme'):
                    try:
                        widget.update_theme(theme)
                        logger.debug(f"✅ Updated theme for {widget.__class__.__name__}")
                    except Exception as e:
                        logger.warning(f"⚠️ Failed to update theme for {widget.__class__.__name__}: {e}")
                elif widget and hasattr(widget, 'apply_enterprise_styling'):
                    try:
                        widget.apply_enterprise_styling()
                        logger.debug(f"✅ Applied enterprise styling to {widget.__class__.__name__}")
                    except Exception as e:
                        logger.warning(f"⚠️ Failed to apply enterprise styling to {widget.__class__.__name__}: {e}")

            # Update specific components that need special handling
            components_to_update = [
                'main_menu',
                'quiz_screen',
                'quiz_setup_screen',
                'settings_menu',  # This is the ProfessionalSettingsScreen
                'training_screen',
                'image_management_screen'
            ]

            for component_name in components_to_update:
                if hasattr(self, component_name):
                    component = getattr(self, component_name)
                    if component and hasattr(component, 'update_theme'):
                        try:
                            component.update_theme(theme)
                            logger.debug(f"✅ Updated {component_name} theme")
                        except Exception as e:
                            logger.warning(f"⚠️ Failed to update {component_name} theme: {e}")
                    else:
                        logger.debug(f"⚠️ Component {component_name} doesn't have update_theme method")

            # Force update of all child widgets
            self._force_widget_style_update(self)

            logger.info(f"✅ THEME PROPAGATION COMPLETE")

        except Exception as e:
            logger.error(f"❌ THEME PROPAGATION FAILED: {e}")

    def _propagate_font_size_to_all_components(self, font_size: int):
        """Propagate font size change to all UI components"""
        try:
            logger.info(f"🔤 FONT SIZE PROPAGATION: Updating all components to {font_size}px")

            # Update specific components that need special handling
            components_to_update = [
                'main_menu',
                'quiz_screen',
                'quiz_setup_screen',
                'settings_menu',  # This is the ProfessionalSettingsScreen
                'training_screen',
                'image_management_screen'
            ]

            for component_name in components_to_update:
                if hasattr(self, component_name):
                    component = getattr(self, component_name)
                    if component and hasattr(component, 'update_font_size'):
                        try:
                            component.update_font_size(font_size)
                            logger.debug(f"✅ Updated {component_name} font size")
                        except Exception as e:
                            logger.warning(f"⚠️ Failed to update {component_name} font size: {e}")
                    else:
                        logger.debug(f"⚠️ Component {component_name} doesn't have update_font_size method")

            # Force update of all child widgets
            self._force_widget_style_update(self)

            logger.info(f"✅ FONT SIZE PROPAGATION COMPLETE")

        except Exception as e:
            logger.error(f"❌ FONT SIZE PROPAGATION FAILED: {e}")

    def _force_widget_style_update(self, widget):
        """Force style update for a widget and all its children"""
        try:
            # Update the widget itself
            if hasattr(widget, 'style'):
                widget.style().unpolish(widget)
                widget.style().polish(widget)

            # Update all child widgets recursively
            for child in widget.findChildren(QWidget):
                if hasattr(child, 'style'):
                    child.style().unpolish(child)
                    child.style().polish(child)

        except Exception as e:
            logger.debug(f"Error forcing style update: {e}")

    def _update_screen_styling(self):
        """Update styling for all screens and components"""
        try:
            # Update main menu
            if hasattr(self, 'main_menu'):
                if hasattr(self.main_menu, 'apply_enterprise_styling'):
                    self.main_menu.apply_enterprise_styling()
                elif hasattr(self.main_menu, 'apply_professional_styling'):
                    self.main_menu.apply_professional_styling()

            # Update quiz screen
            if hasattr(self, 'quiz_screen'):
                if hasattr(self.quiz_screen, 'apply_enterprise_styling'):
                    self.quiz_screen.apply_enterprise_styling()

            # Update settings menu
            if hasattr(self, 'settings_menu'):
                if hasattr(self.settings_menu, 'apply_enterprise_styling'):
                    self.settings_menu.apply_enterprise_styling()

            # Update any other screens in the stack
            if hasattr(self, 'stack'):
                for i in range(self.stack.count()):
                    widget = self.stack.widget(i)
                    if hasattr(widget, 'apply_enterprise_styling'):
                        widget.apply_enterprise_styling()

            logger.debug("Screen styling updated for all components")

        except Exception as e:
            logger.warning(f"Error updating screen styling: {e}")

            if hasattr(self, 'quiz_screen'):
                if hasattr(self.quiz_screen, 'apply_enterprise_styling'):
                    self.quiz_screen.apply_enterprise_styling()
                else:
                    self.quiz_screen.apply_professional_styling()

            if hasattr(self, 'settings_menu'):
                if hasattr(self.settings_menu, 'apply_enterprise_styling'):
                    self.settings_menu.apply_enterprise_styling()
                else:
                    self.settings_menu.apply_professional_styling()

            logger.info("✅ Theme updated successfully with enterprise styling")

        except Exception as e:
            logger.error(f"Error changing theme: {e}")
            # Fallback to enterprise design system theme handling
            try:
                self.design_system.set_theme(theme.lower())
                self.style_manager.apply_theme(theme.lower())
                logger.info("✅ Enterprise theme update completed")
            except Exception as fallback_error:
                logger.error(f"Enterprise theme update failed: {fallback_error}")

    def start_new_quiz_from_setup(self, topic: str, mode: str, question_type: str, difficulty: str = "medium"):
        """Start a new quiz using synchronous generation for immediate response"""
        try:
            # Set cognitive level internally based on difficulty
            cognitive_level = self._determine_cognitive_level(difficulty)

            logger.info(f"🚀 Starting quiz: Topic='{topic}', Mode='{mode}', Type='{question_type}', Cognitive='{cognitive_level}', Difficulty='{difficulty}'")

            # Store current quiz parameters
            self.current_topic = topic
            self.current_mode = mode
            self.current_submode = question_type
            self.current_cognitive_level = cognitive_level
            self.current_difficulty = difficulty

            # Try synchronous generation first for immediate quiz start
            try:
                logger.info("⚡ Attempting synchronous quiz start for immediate response")
                question_data = self.get_next_question_sync()
                if question_data:
                    logger.info("✅ Got initial question synchronously, starting quiz immediately")
                    self._display_quiz_question(question_data, mode)
                    return
                else:
                    logger.warning("⚠️ Synchronous generation returned None, falling back to async")
            except Exception as e:
                logger.warning(f"⚠️ Synchronous generation failed: {e}, falling back to async")

            # Fall back to async generation if sync fails
            logger.info("🔄 Using async generation as fallback")

            # Create quiz parameters dictionary (NO MORE INSTRUCTION-BASED CONTEXT!)
            quiz_params = {
                'topic': topic,
                'difficulty': difficulty,
                'mode': mode,
                'question_type': question_type,
                'cognitive_level': cognitive_level
            }

            # Show loading message and switch to quiz screen
            if hasattr(self, 'statusBar') and self.statusBar():
                self.statusBar().showMessage("🧠 Generating intelligent quiz question...")

            # Use the MCQ manager for proper content retrieval and generation
            self._generate_quiz_with_mcq_manager(quiz_params)

        except Exception as e:
            logger.error(f"❌ Quiz generation failed: {e}")
            # Use instant fallback
            fallback_question = self._generate_fallback_question(topic, question_type)
            self._display_quiz_question(fallback_question, mode)

    def _determine_cognitive_level(self, difficulty: str) -> str:
        """Determine appropriate cognitive level based on difficulty"""
        cognitive_mapping = {
            "easy": "understanding",
            "medium": "applying",
            "hard": "analyzing",
            "expert": "evaluating"
        }
        return cognitive_mapping.get(difficulty.lower(), "understanding")

    def _generate_quiz_with_mcq_manager(self, quiz_params: Dict[str, Any]):
        """Generate quiz using MCQ manager with proper content separation"""
        try:
            logger.info("🎯 Using MCQ Manager for intelligent content-based generation")

            # Get the MCQ manager
            mcq_manager = self._get_mcq_manager()
            if not mcq_manager:
                logger.error("❌ MCQ Manager not available")
                self._generate_fallback_quiz(quiz_params)
                return

            # Switch to quiz screen immediately
            self.show_quiz_screen()

            # Start async generation with proper content retrieval
            import asyncio
            try:
                # Create task for async generation
                task = asyncio.create_task(mcq_manager.generate_quiz_async(quiz_params))

                # Connect to result handling with proper timeout
                self._generation_start_time = time.time()
                self._generation_timeout = 120  # 2 minutes timeout for GGUF generation

                def handle_result():
                    try:
                        if task.done():
                            result = task.result()
                            timer.stop()  # Stop the timer
                            if result and result.get('question'):
                                logger.info("✅ MCQ generation completed successfully")
                                self._display_quiz_question(result, quiz_params.get('mode', 'Casual'))
                            else:
                                logger.warning("⚠️ MCQ generation returned empty result")
                                self._generate_fallback_quiz(quiz_params)
                        else:
                            # Check for timeout
                            elapsed = time.time() - self._generation_start_time
                            if elapsed > self._generation_timeout:
                                logger.warning(f"⚠️ MCQ generation timed out after {elapsed:.1f}s")
                                timer.stop()
                                task.cancel()
                                self._generate_fallback_quiz(quiz_params)
                            else:
                                # Show progress
                                remaining = self._generation_timeout - elapsed
                                if hasattr(self, 'statusBar') and self.statusBar():
                                    self.statusBar().showMessage(f"🧠 Generating question... ({remaining:.0f}s remaining)")
                    except Exception as e:
                        logger.error(f"❌ Error handling MCQ result: {e}")
                        timer.stop()
                        self._generate_fallback_quiz(quiz_params)

                # Use QTimer to check task completion more frequently
                from PyQt5.QtCore import QTimer
                import time
                timer = QTimer()
                timer.timeout.connect(handle_result)
                timer.start(2000)  # Check every 2 seconds

                # Show initial progress message
                if hasattr(self, 'statusBar') and self.statusBar():
                    self.statusBar().showMessage("🧠 Generating intelligent question with AI model...")

            except Exception as e:
                logger.error(f"❌ Failed to create async task: {e}")
                self._generate_fallback_quiz(quiz_params)

        except Exception as e:
            logger.error(f"❌ MCQ Manager generation failed: {e}")
            self._generate_fallback_quiz(quiz_params)

    def _generate_fallback_quiz(self, quiz_params: Dict[str, Any]):
        """Generate a fallback quiz when advanced generation fails"""
        try:
            topic = quiz_params.get('topic', 'General Knowledge')
            question_type = quiz_params.get('question_type', 'Multiple Choice')
            mode = quiz_params.get('mode', 'Casual')

            logger.info(f"🔄 Generating fallback quiz for {topic}")
            fallback_question = self._generate_fallback_question(topic, question_type)
            self._display_quiz_question(fallback_question, mode)

        except Exception as e:
            logger.error(f"❌ Fallback generation failed: {e}")
            self.show_error("Failed to generate quiz question")

    def generate_next_question(self):
        """Generate the next question in the current quiz session without restarting"""
        try:
            logger.info("🔄 Generating next question in current quiz session")

            # Use existing quiz parameters
            topic = getattr(self, 'current_topic', 'general knowledge')
            mode = getattr(self, 'current_mode', 'Casual')
            question_type = getattr(self, 'current_submode', 'Multiple Choice')
            cognitive_level = getattr(self, 'current_cognitive_level', 'understanding')
            difficulty = getattr(self, 'current_difficulty', 'medium')

            # Create context for question generation
            context = f"Generate a {question_type.lower()} question about {topic}. "
            context += f"The question should be suitable for {mode.lower()} mode learning. "
            context += f"Target cognitive level: {cognitive_level} (Bloom's taxonomy). "
            context += f"Difficulty level: {difficulty}."

            logger.info(f"📝 Next question params: Topic='{topic}', Mode='{mode}', Type='{question_type}'")

            # Use process-based model server for next question too (CRASH-PROOF)
            self._generate_quiz_with_model_server(topic, mode, question_type, difficulty, cognitive_level)

        except Exception as e:
            logger.error(f"❌ Next question generation failed: {e}")
            # Use instant fallback
            topic = getattr(self, 'current_topic', 'general knowledge')
            question_type = getattr(self, 'current_submode', 'Multiple Choice')
            mode = getattr(self, 'current_mode', 'Casual')
            fallback_question = self._generate_fallback_question(topic, question_type)
            self._display_quiz_question(fallback_question, mode)

    def get_next_question_sync(self) -> dict:
        """
        Synchronously get the next question for quiz controller.
        This method returns a question immediately without async operations.
        """
        try:
            print("DEBUG_QUIZ_SYNC: get_next_question_sync called!")
            logger.info("🔄 Getting next question synchronously for quiz controller")

            # Use existing quiz parameters
            topic = getattr(self, 'current_topic', 'general knowledge')
            mode = getattr(self, 'current_mode', 'Casual')
            question_type = getattr(self, 'current_submode', 'Multiple Choice')

            # Create context for question generation
            context = f"Generate a {question_type.lower()} question about {topic}. "
            context += f"The question should be suitable for {mode.lower()} mode learning."

            # Determine difficulty based on mode
            difficulty = "hard" if mode == "Serious" else "medium"

            logger.info(f"📝 Sync question params: Topic='{topic}', Mode='{mode}', Type='{question_type}'")

            # Try to use MCQ manager for instant generation
            try:
                print("DEBUG_QUIZ_SYNC: Trying to get MCQ manager...")
                mcq_manager = self._get_mcq_manager()
                print(f"DEBUG_QUIZ_SYNC: MCQ manager: {mcq_manager}")
                if mcq_manager and mcq_manager.is_instant_available():
                    print("DEBUG_QUIZ_SYNC: MCQ manager instant available!")
                    logger.info("⚡ Using MCQ manager instant generation for sync question")

                    # Use asyncio to run the async method synchronously
                    import asyncio
                    try:
                        # Try to get existing event loop
                        loop = asyncio.get_event_loop()
                        if loop.is_running():
                            # If loop is running, we can't use run_until_complete
                            # Fall back to intelligent fallback
                            logger.info("⚡ Event loop running, using intelligent fallback")
                            return self._generate_fallback_question(topic, question_type)
                        else:
                            # Loop exists but not running, use it
                            result = loop.run_until_complete(mcq_manager.generate_quiz_async(context, difficulty))
                    except RuntimeError:
                        # No event loop, create one
                        result = asyncio.run(mcq_manager.generate_quiz_async(context, difficulty))

                    if result and result.get('question'):
                        logger.info("✅ MCQ manager generated sync question successfully")
                        return self._format_question_for_display(result)
                    else:
                        logger.warning("⚠️ MCQ manager returned empty result, using fallback")

            except Exception as e:
                logger.warning(f"⚠️ MCQ manager sync generation failed: {e}")

            # Fall back to intelligent fallback question
            print("DEBUG_QUIZ_SYNC: Using intelligent fallback")
            logger.info("🔄 Using intelligent fallback for sync question")
            fallback_result = self._generate_fallback_question(topic, question_type)
            print(f"DEBUG_QUIZ_SYNC: Fallback result: {fallback_result.get('question', 'No question') if fallback_result else 'None'}")
            return fallback_result

        except Exception as e:
            logger.error(f"❌ Sync question generation failed: {e}")
            # Emergency fallback
            return self._generate_emergency_fallback_question()

    def _format_question_for_display(self, mcq_result: dict) -> dict:
        """Format MCQ manager result for quiz display"""
        try:
            # MCQ manager returns different format, standardize it
            formatted = {
                "question": mcq_result.get('question', 'Sample question'),
                "options": mcq_result.get('options', ['Option A', 'Option B', 'Option C', 'Option D']),
                "correct_answer": mcq_result.get('correct_answer', 'A'),
                "explanation": mcq_result.get('explanation', 'No explanation available'),
                "topic": getattr(self, 'current_topic', 'general knowledge'),
                "question_type": getattr(self, 'current_submode', 'Multiple Choice'),
                "generation_method": "mcq_manager_instant",
                "difficulty": mcq_result.get('difficulty', 'medium')
            }

            # Ensure correct_answer format (should be letter like 'A', 'B', etc.)
            correct_answer = formatted['correct_answer']
            if correct_answer not in ['A', 'B', 'C', 'D']:
                # Try to convert from option text to letter
                options = formatted['options']
                if correct_answer in options:
                    formatted['correct_answer'] = chr(ord('A') + options.index(correct_answer))
                else:
                    formatted['correct_answer'] = 'A'  # Default fallback

            # Add correct_option_letter for compatibility
            formatted['correct_option_letter'] = formatted['correct_answer']

            return formatted

        except Exception as e:
            logger.error(f"❌ Failed to format question: {e}")
            return self._generate_emergency_fallback_question()

    def _generate_emergency_fallback_question(self) -> dict:
        """Generate an emergency fallback question when all else fails"""
        return {
            "question": "What is the most important principle in learning?",
            "options": [
                "Consistent practice and review",
                "Memorizing without understanding",
                "Avoiding challenging topics",
                "Learning only when motivated"
            ],
            "correct_answer": "A",
            "correct_option_letter": "A",
            "explanation": "Consistent practice and review is fundamental to effective learning and retention.",
            "topic": "Learning",
            "question_type": "Multiple Choice",
            "generation_method": "emergency_fallback",
            "difficulty": "medium"
        }

    def load_new_question(self) -> dict:
        """
        Legacy method for loading new questions.
        This provides compatibility with older quiz controller implementations.
        """
        try:
            logger.info("🔄 Loading new question via legacy method")
            # Delegate to the synchronous method
            return self.get_next_question_sync()
        except Exception as e:
            logger.error(f"❌ Legacy question loading failed: {e}")
            return self._generate_emergency_fallback_question()

    def _generate_quiz_with_model_server(self, topic: str, mode: str, question_type: str, difficulty: str, cognitive_level: str = "understanding"):
        """Generate quiz using process-based model server (CRASH-PROOF)"""
        try:
            logger.info("🏰 Using process-based model server for maximum stability")

            # Create enhanced prompt with cognitive level
            cognitive_instructions = {
                "understanding": "The question should test comprehension and explanation of concepts.",
                "applying": "The question should require using knowledge in new situations or solving problems.",
                "analyzing": "The question should require breaking down information and examining relationships.",
                "evaluating": "The question should require making judgments, critiques, or assessments.",
                "remembering": "The question should test recall of facts and basic concepts.",
                "creating": "The question should require producing new ideas or original solutions."
            }

            cognitive_instruction = cognitive_instructions.get(cognitive_level, cognitive_instructions["understanding"])

            # Create prompt for the model server
            prompt = f"""Generate a {difficulty} {question_type.lower()} question about {topic}.

The question should be suitable for {mode.lower()} mode learning.
Cognitive Level: {cognitive_level.title()} (Bloom's Taxonomy)
Instruction: {cognitive_instruction}

Format the response as a JSON object with:
- question: the main question text
- options: array of 4 answer choices (for multiple choice)
- correct_answer: the correct answer
- explanation: brief explanation of why the answer is correct

Topic: {topic}
Difficulty: {difficulty}
Type: {question_type}
Cognitive Level: {cognitive_level}"""

            # Start model server if not running
            if not self.model_client.is_running:
                logger.info("🚀 Starting model server...")
                if not self.model_client.start_server():
                    logger.error("❌ Failed to start model server, using fallback")
                    self._use_fallback_generation(topic, mode, question_type)
                    return

            # Generate question using isolated model server
            from ..core.model_server_client import generate_mcq_isolated

            # Show loading indicator
            self._show_generation_progress("Generating question with AI model...")

            # Use QTimer to make the call non-blocking
            QTimer.singleShot(100, lambda: self._call_model_server_async(prompt, topic, mode, question_type))

        except Exception as e:
            logger.error(f"❌ Model server generation failed: {e}")
            self._use_fallback_generation(topic, mode, question_type)

    def _call_model_server_async(self, prompt: str, topic: str, mode: str, question_type: str):
        """Call model server asynchronously to avoid blocking UI"""
        try:
            from ..core.model_server_client import generate_mcq_isolated

            # This runs in a separate process, so it won't crash the UI
            result = generate_mcq_isolated(prompt, max_tokens=300)

            if result and result.get("success"):
                logger.info("✅ Model server generated question successfully")
                question_data = self._parse_model_server_response(result.get("result", ""))
                self._display_quiz_question(question_data, mode)
            else:
                logger.warning("⚠️ Model server returned unsuccessful result, using fallback")
                self._use_fallback_generation(topic, mode, question_type)

        except Exception as e:
            logger.error(f"❌ Model server call failed: {e}")
            self._use_fallback_generation(topic, mode, question_type)
        finally:
            self._hide_generation_progress()

    def _parse_model_server_response(self, response_text: str) -> dict:
        """Parse the model server response into a question format"""
        try:
            import json
            import re

            # Try to extract JSON from the response
            json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
            if json_match:
                json_str = json_match.group(0)
                question_data = json.loads(json_str)

                # Validate required fields
                if all(key in question_data for key in ['question', 'options', 'correct_answer']):
                    return question_data

            # If JSON parsing fails, create structured question from text
            return self._create_question_from_text(response_text)

        except Exception as e:
            logger.error(f"❌ Failed to parse model response: {e}")
            return self._create_question_from_text(response_text)

    def _create_question_from_text(self, text: str) -> dict:
        """Create a structured question from unstructured text"""
        lines = text.strip().split('\n')
        question = lines[0] if lines else "What is the main concept?"

        # Extract options if they exist
        options = []
        for line in lines[1:]:
            if line.strip() and (line.strip().startswith(('A)', 'B)', 'C)', 'D)', '1.', '2.', '3.', '4.'))):
                options.append(line.strip())

        # If no options found, create generic ones
        if len(options) < 4:
            options = [
                "Option A",
                "Option B",
                "Option C",
                "Option D"
            ]

        return {
            "question": question,
            "options": options[:4],  # Take only first 4
            "correct_answer": options[0] if options else "Option A",
            "explanation": "Generated by AI model"
        }

    def _use_fallback_generation(self, topic: str, mode: str, question_type: str):
        """Use fallback generation when model server fails"""
        logger.info("🔄 Using fallback generation method")
        fallback_question = self._generate_fallback_question(topic, question_type)
        self._display_quiz_question(fallback_question, mode)

    def _show_generation_progress(self, message: str):
        """Show progress indicator for question generation"""
        # This could be enhanced with a proper progress dialog
        if hasattr(self, 'statusBar') and self.statusBar():
            self.statusBar().showMessage(message)

    def _hide_generation_progress(self):
        """Hide progress indicator"""
        if hasattr(self, 'statusBar') and self.statusBar():
            self.statusBar().clearMessage()

    def _start_model_preloading(self):
        """Start preloading the default model in background"""
        try:
            logger.info("🔥 Starting model preloading in background...")
            self.is_model_preloading = True
            self.async_model_manager.preload_default_model()
        except Exception as e:
            logger.error(f"❌ Failed to start model preloading: {e}")
            self.is_model_preloading = False

    def _create_mcq_prompt(self, context: str, difficulty: str) -> str:
        """Create a prompt for MCQ generation"""
        return f"""Generate a multiple choice question based on the following:

Context: {context}
Difficulty: {difficulty}

Format the response as:
Question: [question text]
A) [option A]
B) [option B]
C) [option C]
D) [option D]
Correct Answer: [A/B/C/D]
Explanation: [brief explanation]

Make sure the question is appropriate for {difficulty} difficulty."""

    def _try_mcq_fallback(self, error_message: str):
        """Try fallback MCQ generation methods"""
        try:
            logger.info("🔄 Trying MCQ fallback methods...")

            # Use current quiz parameters if available
            topic = getattr(self, 'current_topic', 'general knowledge')
            question_type = getattr(self, 'current_submode', 'Multiple Choice')
            mode = getattr(self, 'current_mode', 'Casual')

            # Generate fallback question
            fallback_question = self._generate_fallback_question(topic, question_type)
            self._display_quiz_question(fallback_question, mode)

            logger.info("✅ Fallback question displayed successfully")

        except Exception as e:
            logger.error(f"❌ All fallback methods failed: {e}")
            self.show_error("Unable to generate quiz question. Please try again later.")

    def _select_best_generation_mode(self, mcq_manager):
        """Select the best available generation mode"""
        # Priority order: offline (7B models) > online > instant
        if mcq_manager.is_offline_available():
            mcq_manager.set_offline_mode(True)
            mcq_manager.set_instant_mode(False)
            return "offline"
        elif hasattr(mcq_manager, 'online_generator') and mcq_manager.online_generator:
            mcq_manager.set_offline_mode(False)
            mcq_manager.set_instant_mode(False)
            return "online"
        else:
            mcq_manager.set_instant_mode(True)
            mcq_manager.set_offline_mode(False)
            return "instant"

    def _generate_fallback_question(self, topic: str, question_type: str):
        """Generate a MUCH BETTER fallback question - NO MORE GENERIC GARBAGE"""

        # Topic-specific, factual questions instead of meta-learning bullshit
        topic_questions = {
            "magnetism": {
                "question": "According to the fundamental principles of magnetism, what happens when like magnetic poles are brought close together?",
                "options": {
                    "A": "They attract each other with increasing force",
                    "B": "They repel each other with a force that increases as distance decreases",
                    "C": "They have no effect on each other",
                    "D": "They create a neutral magnetic field"
                },
                "correct": "B",
                "explanation": "Like magnetic poles (north-north or south-south) repel each other, and this repulsive force increases as the distance between them decreases, following an inverse square relationship."
            },
            "physics": {
                "question": "According to Newton's first law of motion, what happens to an object at rest when no net force acts upon it?",
                "options": {
                    "A": "It begins to move at constant velocity",
                    "B": "It remains at rest indefinitely",
                    "C": "It accelerates slowly",
                    "D": "It moves in a circular path"
                },
                "correct": "B",
                "explanation": "Newton's first law states that an object at rest stays at rest unless acted upon by an unbalanced net force. This is the principle of inertia."
            },
            "chemistry": {
                "question": "In the periodic table, what determines an element's chemical properties?",
                "options": {
                    "A": "The number of neutrons in the nucleus",
                    "B": "The total mass of the atom",
                    "C": "The number of valence electrons",
                    "D": "The physical state at room temperature"
                },
                "correct": "C",
                "explanation": "An element's chemical properties are primarily determined by the number of valence electrons in its outermost shell, which determines how it bonds with other elements."
            },
            "biology": {
                "question": "What is the primary function of DNA in living organisms?",
                "options": {
                    "A": "To provide energy for cellular processes",
                    "B": "To store and transmit genetic information",
                    "C": "To break down waste products",
                    "D": "To regulate body temperature"
                },
                "correct": "B",
                "explanation": "DNA (deoxyribonucleic acid) serves as the molecular blueprint that stores genetic information and passes it from one generation to the next."
            }
        }

        # Get topic-specific question or create a better generic one
        topic_lower = topic.lower()

        if topic_lower in topic_questions:
            question_data = topic_questions[topic_lower].copy()
        else:
            # Even the generic fallback is better than the original garbage
            question_data = {
                "question": f"What is a fundamental characteristic that defines {topic} as a field of study?",
                "options": {
                    "A": f"{topic.title()} follows measurable, predictable patterns and principles",
                    "B": f"{topic.title()} is based entirely on subjective opinions",
                    "C": f"{topic.title()} has no practical applications",
                    "D": f"{topic.title()} contradicts established scientific knowledge"
                },
                "correct": "A",
                "explanation": f"{topic.title()} is characterized by systematic study of measurable phenomena that follow consistent patterns and principles."
            }

        # Add metadata
        question_data.update({
            "topic": topic,
            "question_type": question_type,
            "generation_method": "intelligent_fallback",
            "difficulty": "medium"
        })

        logger.info(f"✅ Generated intelligent fallback question for {topic}")
        return question_data

    def _display_quiz_question(self, question_data, mode):
        """Display the quiz question in the professional quiz screen"""
        try:
            # Ensure professional quiz screen exists (should be created in setup_ui)
            if not hasattr(self, 'quiz_screen'):
                logger.warning("Quiz screen not found, creating new one")
                self.quiz_screen = ProfessionalQuizScreen(self)
                self.stack.addWidget(self.quiz_screen)

            # Configure quiz mode
            self.quiz_screen.set_quiz_mode(mode)

            # Display the question with professional formatting
            self.quiz_screen.display_question(question_data)

            # Switch to professional quiz screen
            self.stack.setCurrentWidget(self.quiz_screen)

            logger.info(f"✅ Displayed question in professional quiz screen (mode: {mode})")

        except Exception as e:
            logger.error(f"Error displaying quiz question: {e}")
            # Fallback to showing error message
            self.show_error(f"Failed to display quiz question: {e}")

    def handle_add_images(self):
        """Handle adding images to the application"""
        try:
            from PyQt5.QtWidgets import QFileDialog, QMessageBox

            # Open file dialog to select images
            files, _ = QFileDialog.getOpenFileNames(
                self,
                "Select Images to Add",
                "",
                "Image Files (*.png *.jpg *.jpeg *.gif *.bmp *.tiff);;All Files (*)"
            )

            if not files:
                return

            # Process selected images
            added_count = 0
            failed_files = []

            for file_path in files:
                try:
                    if self.image_manager and self.image_manager.add_image(file_path):
                        added_count += 1
                    else:
                        failed_files.append(file_path)
                except Exception as e:
                    logger.error(f"Failed to add image {file_path}: {e}")
                    failed_files.append(file_path)

            # Show results
            if added_count > 0:
                message = f"Successfully added {added_count} image(s)."
                if failed_files:
                    message += f"\n{len(failed_files)} file(s) failed to add."
                QMessageBox.information(self, "Images Added", message)
            else:
                QMessageBox.warning(self, "No Images Added", "No images were successfully added.")

            # Update storage info
            self._update_status()

        except Exception as e:
            logger.error(f"Error in handle_add_images: {e}")
            QMessageBox.critical(self, "Error", f"Failed to add images: {str(e)}")

    def show_training_dialog(self):
        """Show the AI training dialog"""
        try:
            # Create and show the AI training dialog
            training_dialog = AITrainingDialog(self)

            # Connect signals for training events
            training_dialog.training_started.connect(self._on_training_started)
            training_dialog.training_completed.connect(self._on_training_dialog_completed)
            training_dialog.training_cancelled.connect(self._on_training_cancelled)

            # Show the dialog
            training_dialog.show()

        except Exception as e:
            logger.error(f"Failed to show training dialog: {e}")
            self.show_error(f"Failed to open training dialog: {e}")



    def _on_training_started(self, config: dict):
        """Handle training started event"""
        try:
            logger.info(f"Training started with config: {config}")
            self.statusBar().showMessage("AI model training started...")
        except Exception as e:
            logger.error(f"Error handling training start: {e}")

    def _on_training_dialog_completed(self, success: bool, message: str):
        """Handle training completion from dialog"""
        try:
            if success:
                self.statusBar().showMessage("AI model training completed successfully!")
                self.show_info(f"Training completed successfully!\n{message}")
            else:
                self.statusBar().showMessage("AI model training failed")
                self.show_error(f"Training failed: {message}")
        except Exception as e:
            logger.error(f"Error handling training completion: {e}")

    def _on_training_cancelled(self):
        """Handle training cancellation"""
        try:
            self.statusBar().showMessage("AI model training cancelled")
            logger.info("Training cancelled by user")
        except Exception as e:
            logger.error(f"Error handling training cancellation: {e}")

    def start_automated_training(self):
        """Start automated training process"""
        try:
            # Create and show training dialog
            dialog = TrainingConfigDialog(self)
            if dialog.exec_() == QDialog.Accepted:
                config = dialog.get_config()
                
                # Create training thread
                self.training_thread = TrainingThread(
                    train_file=None,  # Will be set by the training process
                    base_model=None,  # Will be set by the training process
                    config=config,
                    parent=self
                )
                
                # Connect signals
                self.training_thread.progress_update.connect(self._on_training_progress)
                self.training_thread.finished_training.connect(self._on_training_finished)
                
                # Start training
                self.training_thread.start()
                
        except Exception as e:
            logger.error(f"Failed to start training: {e}")
            self.show_error(f"Failed to start training: {e}")
            
    def _on_training_progress(self, progress: int, accuracy: float):
        """Handle training progress updates"""
        try:
            self.statusBar().showMessage(f"Training Progress: {progress}% (Accuracy: {accuracy:.2%})")
        except Exception as e:
            logger.error(f"Error updating training progress: {e}")
            
    def _on_training_finished(self, success: bool, final_accuracy: float, error_msg: str):
        """Handle training completion"""
        try:
            if success:
                self.show_info(f"Training completed successfully! Final accuracy: {final_accuracy:.2%}")
            else:
                error_msg = error_msg if error_msg else "Training failed"
                self.show_error(error_msg)
        except Exception as e:
            logger.error(f"Error handling training completion: {e}")
            self.show_error(f"Error handling training completion: {e}")

    def update_storage_info(self):
        """Update storage information display"""
        try:
            # Get storage info from managers
            image_usage = self.image_manager.get_storage_usage()
            model_usage = self.model_manager.get_storage_usage()
            book_usage = self.storage_manager.get_storage_usage()
            
            # Calculate total usage
            total_usage = image_usage + model_usage + book_usage
            total_limit = (
                self.config.get_setting('storage_config.image_cache_limit', 50 * 1024 * 1024) +
                self.config.get_setting('storage_config.model_cache_limit', 100 * 1024 * 1024) +
                self.config.get_setting('storage_config.book_storage_limit', 20 * 1024 * 1024)
            )
            
            # Update storage label
            usage_mb = total_usage / (1024 * 1024)
            limit_mb = total_limit / (1024 * 1024)
            self.main_menu.storage_label.setText(f"{usage_mb:.1f} MB used / {limit_mb:.1f} MB limit")
            
        except Exception as e:
            logger.error(f"Failed to update storage info: {e}")
            self.show_error("Failed to update storage information")