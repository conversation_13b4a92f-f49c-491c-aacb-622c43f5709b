#!/usr/bin/env python3
"""
Optimized Knowledge App Main Entry Point
Ultra-fast startup with aggressive lazy loading
"""

import sys
import time
import os
from pathlib import Path

# PHASE 1: Critical environment setup (target: <50ms)
start_time = time.time()

# Optimize Python environment immediately
os.environ['PYTHONUNBUFFERED'] = '1'
os.environ['TOKENIZERS_PARALLELISM'] = 'false'
os.environ['TRANSFORMERS_VERBOSITY'] = 'error'
os.environ['DATASETS_VERBOSITY'] = 'error'
sys.dont_write_bytecode = True

# PHASE 2: Minimal imports only (target: <100ms)
import logging
import warnings

# Suppress all warnings for clean startup
warnings.filterwarnings("ignore")

# Setup minimal logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# PHASE 3: Import startup profiler
try:
    from src.knowledge_app.core.startup_profiler import start_phase, end_phase, get_summary
    profiling_enabled = True
except ImportError:
    # Fallback if profiler not available
    def start_phase(name): pass
    def end_phase(name): pass
    def get_summary(): return {}
    profiling_enabled = False

logger.info("🚀 Knowledge App - Ultra-Fast Startup Mode")

# PHASE 4: Core application import (deferred)
start_phase("core_imports")

def main():
    """Main application entry point with ultra-fast startup"""
    try:
        # Import application bootstrapper only when needed
        from src.knowledge_app.core.application_bootstrapper import ApplicationBootstrapper
        end_phase("core_imports")
        
        # Create and run application
        start_phase("application_startup")
        bootstrapper = ApplicationBootstrapper()
        result = bootstrapper.bootstrap()  # Fixed: use bootstrap() not run()
        end_phase("application_startup")
        
        # Log performance summary
        if profiling_enabled:
            summary = get_summary()
            total_time = time.time() - start_time
            logger.info(f"🎯 Startup completed in {total_time:.2f}s")
            logger.info(f"📊 Performance summary: {len(summary.get('phases', []))} phases")
        
        return result
        
    except KeyboardInterrupt:
        logger.info("👋 Application interrupted by user")
        return 0
    except Exception as e:
        logger.error(f"❌ Application failed: {e}")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
